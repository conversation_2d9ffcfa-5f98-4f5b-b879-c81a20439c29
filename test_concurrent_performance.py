#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化并发性能测试脚本 - 测试缓存优化后的API性能
重点测试：HomePage、问卷计算、预后分析等高频访问组件
"""

import requests
import time
import statistics
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# 测试配置
BASE_URL = "http://127.0.0.1:8000"
CONCURRENT_USERS = [1, 5, 10, 20]  # 并发用户数
TEST_REQUESTS = 50  # 每轮测试请求数

# 从token文件读取测试token
def load_test_token():
    try:
        with open('test_token_user_2.txt', 'r') as f:
            return f.read().strip()
    except FileNotFoundError:
        print("⚠️ 未找到test_token_user_2.txt文件，将使用无token测试")
        return None

TEST_TOKEN = load_test_token()

# 测试端点配置
TEST_ENDPOINTS = {
    "HomePage": {
        "url": f"{BASE_URL}/api/HomePage/",
        "method": "GET",
        "headers": {},
        "description": "首页静态内容（已优化缓存2小时）"
    },
    "数据库健康": {
        "url": f"{BASE_URL}/api/db_health_api/connection-pool-status/",
        "method": "GET",
        "headers": {"Authorization": f"Bearer {TEST_TOKEN}"} if TEST_TOKEN else {},
        "description": "数据库连接池状态（实时数据）"
    },
    "八字分析-提示词": {
        "url": f"{BASE_URL}/api/routertest1_api/bazi/prompts/",
        "method": "GET",
        "headers": {"Authorization": f"Bearer {TEST_TOKEN}"} if TEST_TOKEN else {},
        "description": "八字分析提示词API（现有API，有缓存）"
    },
    "八字分析-类型": {
        "url": f"{BASE_URL}/api/routertest1_api/bazi/analysis-types/",
        "method": "GET",
        "headers": {"Authorization": f"Bearer {TEST_TOKEN}"} if TEST_TOKEN else {},
        "description": "八字分析类型API（现有API，有缓存）"
    },
    "预后分析-疗法分类": {
        "url": f"{BASE_URL}/api/routertest1_api/prognosis/therapy-classifications/",
        "method": "GET",
        "headers": {"Authorization": f"Bearer {TEST_TOKEN}"} if TEST_TOKEN else {},
        "description": "疗法分类API（现有API，有缓存）"
    },
    "预后分析-症状列表": {
        "url": f"{BASE_URL}/api/routertest1_api/prognosis/symptoms/?page=1&page_size=20",
        "method": "GET",
        "headers": {"Authorization": f"Bearer {TEST_TOKEN}"} if TEST_TOKEN else {},
        "description": "症状列表API（现有API，有分页和缓存）"
    },
    "问卷系统-状态": {
        "url": f"{BASE_URL}/api/questionnaire_api/health/",
        "method": "GET",
        "headers": {"Authorization": f"Bearer {TEST_TOKEN}"} if TEST_TOKEN else {},
        "description": "问卷系统健康检查（现有API）"
    }
}

def make_request(url, method="GET", headers=None, data=None):
    """发送单个HTTP请求"""
    start_time = time.time()
    try:
        if method == "GET":
            response = requests.get(url, headers=headers or {}, timeout=10)
        else:
            response = requests.post(url, headers=headers or {}, json=data or {}, timeout=10)

        response_time = time.time() - start_time
        success = response.status_code == 200

        # 检查缓存头
        cache_hit = 'Cache-Control' in response.headers or 'ETag' in response.headers

        return {
            'response_time': response_time,
            'success': success,
            'status_code': response.status_code,
            'cache_hit': cache_hit,
            'error': None
        }
    except Exception as e:
        response_time = time.time() - start_time
        return {
            'response_time': response_time,
            'success': False,
            'status_code': 0,
            'cache_hit': False,
            'error': str(e)
        }

def run_concurrent_test(endpoint_name, config, concurrent_users, total_requests):
    """运行并发测试"""
    print(f"\n🚀 开始测试 {endpoint_name} - {concurrent_users} 并发用户，总请求数 {total_requests}")
    print(f"   📝 {config['description']}")

    results = []
    start_time = time.time()

    def worker():
        return make_request(
            config["url"],
            config["method"],
            config.get("headers", {}),
            config.get("data", {})
        )

    # 使用线程池执行并发请求
    with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
        futures = [executor.submit(worker) for _ in range(total_requests)]

        for future in as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                results.append({
                    'response_time': 0,
                    'success': False,
                    'status_code': 0,
                    'cache_hit': False,
                    'error': str(e)
                })

    end_time = time.time()
    total_duration = end_time - start_time

    # 计算统计信息
    response_times = [r['response_time'] for r in results]
    success_count = sum(1 for r in results if r['success'])
    cache_hits = sum(1 for r in results if r['cache_hit'])

    stats = {
        'total_requests': len(results),
        'success_count': success_count,
        'success_rate': (success_count / len(results) * 100) if results else 0,
        'cache_hit_rate': (cache_hits / success_count * 100) if success_count > 0 else 0,
        'avg_response_time': statistics.mean(response_times) if response_times else 0,
        'min_response_time': min(response_times) if response_times else 0,
        'max_response_time': max(response_times) if response_times else 0,
        'median_response_time': statistics.median(response_times) if response_times else 0,
        'qps': len(results) / total_duration if total_duration > 0 else 0,
        'total_duration': total_duration
    }

    return stats, results

def print_test_results(endpoint_name, concurrent_users, stats):
    """打印测试结果"""
    print(f"\n📊 {endpoint_name} 测试结果 ({concurrent_users} 并发用户):")
    print(f"   ✅ 总请求数: {stats['total_requests']}")
    print(f"   ✅ 成功率: {stats['success_rate']:.1f}%")
    print(f"   ⚡ 缓存命中率: {stats['cache_hit_rate']:.1f}%")
    print(f"   ⏱️  平均响应时间: {stats['avg_response_time']*1000:.1f}ms")
    print(f"   ⏱️  中位数响应时间: {stats['median_response_time']*1000:.1f}ms")
    print(f"   ⏱️  最大响应时间: {stats['max_response_time']*1000:.1f}ms")
    print(f"   🔥 QPS: {stats['qps']:.1f}")
    print(f"   ⏰ 总耗时: {stats['total_duration']:.2f}秒")

def main():
    """主测试函数"""
    print("🎯 Django API 并发性能测试")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 测试地址: {BASE_URL}")
    print(f"🔑 使用Token: {'是' if TEST_TOKEN else '否'}")
    print(f"👥 并发用户数: {CONCURRENT_USERS}")
    print(f"📊 每轮测试请求数: {TEST_REQUESTS}")

    # 存储所有测试结果
    all_results = {}

    # 对每个端点进行测试
    for endpoint_name, config in TEST_ENDPOINTS.items():
        print(f"\n{'='*60}")
        print(f"🎯 测试端点: {endpoint_name}")
        print(f"📍 URL: {config['url']}")

        endpoint_results = {}

        # 对每个并发级别进行测试
        for concurrent_users in CONCURRENT_USERS:
            try:
                stats, results = run_concurrent_test(endpoint_name, config, concurrent_users, TEST_REQUESTS)
                endpoint_results[concurrent_users] = stats
                print_test_results(endpoint_name, concurrent_users, stats)

                # 测试间隔，让系统恢复
                time.sleep(1)

            except Exception as e:
                print(f"❌ {endpoint_name} ({concurrent_users}并发) 测试失败: {e}")

        all_results[endpoint_name] = endpoint_results

    # 生成汇总报告
    print_summary_report(all_results)

def print_summary_report(all_results):
    """打印汇总报告"""
    print(f"\n{'='*60}")
    print("📈 性能测试汇总报告")
    print("="*60)

    for endpoint_name, endpoint_results in all_results.items():
        print(f"\n🎯 {endpoint_name}:")
        print("   并发数 | QPS   | 平均响应时间 | 缓存命中率 | 成功率")
        print("   -------|-------|-------------|-----------|-------")

        for concurrent_users, stats in endpoint_results.items():
            print(f"   {concurrent_users:6d} | {stats['qps']:5.1f} | {stats['avg_response_time']*1000:8.1f}ms | {stats['cache_hit_rate']:8.1f}% | {stats['success_rate']:5.1f}%")

if __name__ == "__main__":
    main()
