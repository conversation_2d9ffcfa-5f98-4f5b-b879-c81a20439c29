"""
API限流装饰器模块
支持基于用户ID的API调用次数限制，区分普通用户和会员用户
普通用户：按周刷新限额
会员用户：按日刷新限额
"""
import json
import logging
from functools import wraps
from typing import Dict, Optional, Callable, Any
from django.core.cache import cache
from django.http import JsonResponse
from django.utils import timezone
from datetime import datetime, timedelta
from asgiref.sync import sync_to_async
from ninja.errors import HttpError

logger = logging.getLogger(__name__)


class RateLimiter:
    """API限流器类"""
    
    # 默认限额配置
    DEFAULT_LIMITS = {
        'normal_user': 18,      # 普通用户每周限额
        'member_user': 100,      # 会员用户每日限额
    }
    
    def __init__(self, api_name: str, normal_limit: int = None, member_limit: int = None):
        """
        初始化限流器
        
        Args:
            api_name: API名称，用于缓存键
            normal_limit: 普通用户每周限额，为None时使用默认值
            member_limit: 会员用户每日限额，为None时使用默认值
        """
        self.api_name = api_name
        self.normal_limit = normal_limit or self.DEFAULT_LIMITS['normal_user']
        self.member_limit = member_limit or self.DEFAULT_LIMITS['member_user']
    
    def _get_cache_key(self, user_id: int, is_member: bool) -> str:
        """生成缓存键，根据用户身份使用不同的时间周期"""
        now = timezone.now()
        
        if is_member:
            # 会员用户：每日刷新，使用日期作为键
            time_key = now.strftime('%Y-%m-%d')
        else:
            # 普通用户：每周刷新，使用周数作为键（ISO周数）
            year, week, _ = now.isocalendar()
            time_key = f"{year}-W{week:02d}"
        
        return f"rate_limit:{self.api_name}:{user_id}:{time_key}"
    
    def _get_user_limit(self, is_member: bool) -> int:
        """根据用户身份获取限额"""
        return self.member_limit if is_member else self.normal_limit
    
    def _get_reset_time(self, is_member: bool) -> datetime:
        """根据用户身份计算重置时间"""
        now = timezone.now()
        
        if is_member:
            # 会员用户：明天00:00重置
            return now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
        else:
            # 普通用户：下周一00:00重置
            days_until_monday = (7 - now.weekday()) % 7
            if days_until_monday == 0:  # 如果今天是周一，重置时间是下周一
                days_until_monday = 7
            return now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=days_until_monday)

    async def _check_user_membership(self, user_id: int) -> bool:
        """异步检查用户会员身份"""
        try:
            from api.ninja_apis.async_utils import get_async
            from api.models.user_models import UserInfo
            
            user = await get_async(UserInfo, id=user_id)
            if not user:
                return False
            
            # 检查会员状态是否过期
            now = timezone.now()
            if user.is_member and user.membership_end_date:
                if now > user.membership_end_date:
                    return False
            
            return user.is_member
        except Exception as e:
            logger.error(f"检查用户{user_id}会员身份失败: {e}")
            return False
    
    async def check_rate_limit(self, user_id: int) -> Dict[str, Any]:
        """
        检查用户是否超过限流
        
        Returns:
            Dict包含:
            - allowed: bool, 是否允许访问
            - current_count: int, 当前调用次数
            - limit: int, 限额
            - reset_time: str, 重置时间
            - is_member: bool, 是否为会员
        """
        # 检查用户会员身份
        is_member = await self._check_user_membership(user_id)
        cache_key = self._get_cache_key(user_id, is_member)
        user_limit = self._get_user_limit(is_member)
        
        # 从缓存获取当前调用次数
        try:
            current_count = await sync_to_async(cache.get)(cache_key, 0)
        except Exception as e:
            logger.error(f"从缓存获取限流数据失败: {e}")
            current_count = 0
        
        # 计算重置时间
        reset_time = self._get_reset_time(is_member)
        reset_time_str = reset_time.strftime('%Y-%m-%d %H:%M:%S')
        
        return {
            'allowed': current_count < user_limit,
            'current_count': current_count,
            'limit': user_limit,
            'reset_time': reset_time_str,
            'is_member': is_member
        }
    
    async def increment_count(self, user_id: int) -> None:
        """增加用户调用次数"""
        # 先检查会员身份
        is_member = await self._check_user_membership(user_id)
        cache_key = self._get_cache_key(user_id, is_member)
        
        try:
            # 获取当前次数并增加1
            current_count = await sync_to_async(cache.get)(cache_key, 0)
            new_count = current_count + 1
            
            # 设置缓存，根据用户身份设置不同的过期时间
            reset_time = self._get_reset_time(is_member)
            seconds_until_reset = int((reset_time - timezone.now()).total_seconds())
            
            await sync_to_async(cache.set)(cache_key, new_count, timeout=seconds_until_reset)
            
            period = "每日" if is_member else "每周"
            logger.info(f"[RATE_LIMIT] 用户{user_id}({period}) API:{self.api_name} 调用次数: {new_count}")
            
        except Exception as e:
            logger.error(f"更新限流计数失败: {e}")


def rate_limit(api_name: str, normal_limit: int = None, member_limit: int = None):
    """
    API限流装饰器

    Args:
        api_name: API名称
        normal_limit: 普通用户每周限额
        member_limit: 会员用户每日限额

    Usage:
        @rate_limit("chat_api", normal_limit=30, member_limit=300)
        async def my_api_view(request):
            ...
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(request, *args, **kwargs):
            # 获取用户ID
            user_id = getattr(request, 'user_id', None)
            print(f"[RATE_LIMIT_DEBUG] API: {api_name}, user_id: {user_id}, request: {request}")

            if not user_id:
                print(f"[RATE_LIMIT_DEBUG] 用户ID为空，返回401")
                raise HttpError(401, "用户未登录")

            # 🚀 检查是否为测试token，如果是则豁免限流
            try:
                import jwt
                from django.conf import settings
                auth_header = request.META.get('HTTP_AUTHORIZATION', '')
                if auth_header.startswith('Bearer '):
                    token = auth_header[7:]
                    decoded_token = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'], options={"verify_signature": False})
                    if decoded_token.get('test_mode', False):
                        print(f"[RATE_LIMIT_DEBUG] 测试Token豁免限流: {api_name}")
                        return await func(request, *args, **kwargs)
            except Exception:
                pass  # 如果解码失败，继续正常限流检查

            # 创建限流器实例
            limiter = RateLimiter(api_name, normal_limit, member_limit)

            # 检查限流
            limit_info = await limiter.check_rate_limit(user_id)

            if not limit_info['allowed']:
                # 超过限额，抛出HTTP异常
                period = "今日" if limit_info['is_member'] else "本周"
                error_message = f'您{period}的{api_name}调用次数已达上限 ({limit_info["current_count"]}/{limit_info["limit"]})'
                if not limit_info['is_member']:
                    error_message += '，升级会员可享受每日更多调用次数'

                logger.warning(f"[RATE_LIMIT_EXCEEDED] 用户{user_id} API:{api_name} 超过限额 {limit_info['current_count']}/{limit_info['limit']}")

                # 使用字符串而不是字典作为错误消息
                raise HttpError(429, error_message)

            # 🔧 重要修复：将计数操作移到函数执行成功之后
            # 这样缓存命中时就不会增加计数，保持缓存效果

            # 执行原函数
            try:
                response = await func(request, *args, **kwargs)

                # 🚀 只有在函数成功执行后才增加调用次数
                await limiter.increment_count(user_id)

                # 🔧 重新获取当前计数来计算正确的remaining值
                updated_limit_info = await limiter.check_rate_limit(user_id)

                # 🔧 修复：轻量级处理headers，避免破坏缓存机制
                from django.http import JsonResponse, HttpResponse

                # 准备限流headers（使用更新后的计数）
                rate_limit_headers = {
                    'X-RateLimit-Limit': str(limit_info['limit']),
                    'X-RateLimit-Remaining': str(max(0, limit_info['limit'] - updated_limit_info['current_count'])),
                    'X-RateLimit-Reset': limit_info['reset_time']
                }

                # 根据响应类型智能处理
                if isinstance(response, (JsonResponse, HttpResponse)):
                    # 🚀 优化：直接在原响应对象上添加headers，避免重新创建对象
                    # 这样可以保持缓存响应的高性能
                    for key, value in rate_limit_headers.items():
                        response[key] = value

                    print(f"[RATE_LIMIT] ✅ {type(response).__name__}已添加限流headers，保持原对象")
                    return response

                else:
                    # 对于字典、列表等数据类型，直接返回，让Django Ninja处理
                    # 这些响应类型会被Django Ninja自动序列化，无法直接添加headers
                    # 限流信息可以通过日志记录，或者在响应数据中添加元信息
                    print(f"[RATE_LIMIT] ✅ 数据类型响应({type(response).__name__})，由Django Ninja处理序列化")

                    # 可选：在响应数据中添加限流信息（如果是字典类型）
                    if isinstance(response, dict) and '_meta' not in response:
                        # 只有在响应是字典且没有_meta字段时才添加元信息
                        response_copy = response.copy()
                        response_copy['_rate_limit_info'] = {
                            'limit': limit_info['limit'],
                            'remaining': max(0, limit_info['limit'] - updated_limit_info['current_count']),
                            'reset_time': limit_info['reset_time']
                        }
                        return response_copy

                    return response

            except Exception as e:
                # 如果函数执行失败，不增加计数（因为没有成功调用）
                logger.error(f"API {api_name} 执行失败: {e}")
                raise

        return wrapper
    return decorator


def ip_rate_limit(api_name: str, limit_per_hour: int = 60):
    """
    基于IP地址的限流装饰器，适用于登录等不需要用户身份验证的API

    Args:
        api_name: API名称
        limit_per_hour: 每小时限额

    Usage:
        @ip_rate_limit("login_api", limit_per_hour=30)
        async def login_api_view(request):
            ...
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(request, *args, **kwargs):
            # 获取客户端IP地址
            client_ip = request.META.get('HTTP_X_FORWARDED_FOR')
            if client_ip:
                client_ip = client_ip.split(',')[0].strip()
            else:
                client_ip = request.META.get('REMOTE_ADDR', 'unknown')

            print(f"[IP_RATE_LIMIT_DEBUG] API: {api_name}, IP: {client_ip}")

            # 生成缓存键（基于IP和小时）
            now = timezone.now()
            hour_key = now.strftime('%Y-%m-%d-%H')
            cache_key = f"ip_rate_limit:{api_name}:{client_ip}:{hour_key}"

            try:
                # 获取当前调用次数
                current_count = await sync_to_async(cache.get)(cache_key, 0)

                if current_count >= limit_per_hour:
                    logger.warning(f"[IP_RATE_LIMIT_EXCEEDED] IP:{client_ip} API:{api_name} 超过限额 {current_count}/{limit_per_hour}")
                    raise HttpError(429, f"请求过于频繁，请稍后再试（每小时限制{limit_per_hour}次）")

                # 执行原函数
                response = await func(request, *args, **kwargs)

                # 成功执行后增加计数
                new_count = current_count + 1
                # 设置缓存，过期时间为1小时
                await sync_to_async(cache.set)(cache_key, new_count, timeout=3600)

                logger.info(f"[IP_RATE_LIMIT] IP:{client_ip} API:{api_name} 调用次数: {new_count}/{limit_per_hour}")

                return response

            except HttpError:
                # 重新抛出HTTP错误
                raise
            except Exception as e:
                logger.error(f"IP限流检查失败: {e}")
                # 限流检查失败时允许请求通过，避免影响正常功能
                return await func(request, *args, **kwargs)

        return wrapper
    return decorator


# 预定义的装饰器，适用于常见的API（使用默认限额）
chat_rate_limit = rate_limit("chat_api")
medical_case_study_rate_limit = rate_limit("medical_case_study_api")
medical_case_scoring_rate_limit = rate_limit("medical_case_scoring_api")


class RateLimitStats:
    """限流统计工具类"""
    
    @staticmethod
    async def get_user_stats(user_id: int, api_name: str) -> Dict[str, Any]:
        """获取用户的API调用统计"""
        limiter = RateLimiter(api_name)
        
        # 先检查用户会员身份
        is_member = await limiter._check_user_membership(user_id)
        cache_key = limiter._get_cache_key(user_id, is_member)
        user_limit = limiter._get_user_limit(is_member)
        
        try:
            current_count = await sync_to_async(cache.get)(cache_key, 0)
            reset_time = limiter._get_reset_time(is_member)
            reset_time_str = reset_time.strftime('%Y-%m-%d %H:%M:%S')
            
            return {
                'api_name': api_name,
                'current_count': current_count,
                'limit': user_limit,
                'remaining': max(0, user_limit - current_count),
                'reset_time': reset_time_str,
                'is_member': is_member,
                'period': '每日' if is_member else '每周'
            }
        except Exception as e:
            logger.error(f"获取用户{user_id} API:{api_name}统计失败: {e}")
            return {}
    
    @staticmethod
    async def get_all_api_stats(user_id: int) -> Dict[str, Any]:
        """获取用户所有API的调用统计"""
        api_names = ["chat_api", "medical_case_study_api", "medical_case_scoring_api"]
        stats = {}
        
        for api_name in api_names:
            stats[api_name] = await RateLimitStats.get_user_stats(user_id, api_name)
        
        return stats


# ===============================================
# 📊 项目接口限额配置总结
# ===============================================
"""
🎯 【项目接口限额配置总览】
已为以下接口模块添加了限流装饰器，遵循统一的限额规范：
普通用户：每周限额，会员用户：每日限额

📱 WebSocket消费者限额：
- chat_api: 普通用户每周18次，会员用户每日100次
  * ArkChatConsumer (推荐)
  * ArkChatVIPConsumer  
  * DoubanChatConsumer
  * DeepseekChatConsumer (已弃用)
  * DeepseekChatVIPConsumer (已弃用)
- symptom_analysis_api: 普通用户每周15次，会员用户每日50次
  * Deepseek2SingleSymptom

🌐 HTTP API接口限额：

📋 问卷系统 (questionnaire_api):
- questionnaire_api: 50/200 - 问卷查询
- questionnaire_check_api: 30/100 - 检查填写状态
- constitution_calculation_api: 20/80 - 体质计算
- questionnaire_analysis_api: 15/60 - 问卷分析
- latest_calculation_api: 40/150 - 获取最新计算结果
- calculate_scores_api: 25/100 - 分数计算
- user_questionnaires_api: 30/120 - 用户问卷列表
- calculation_histories_api: 35/140 - 计算历史列表
- calculation_history_detail_api: 40/160 - 历史详情
- delete_calculation_history_api: 10/40 - 删除历史
- update_user_info_api: 20/80 - 更新用户信息
- update_symptoms_api: 15/60 - 更新症状信息
- get_user_info_api: 50/200 - 获取用户信息

🔐 认证系统 (auth_api):
- refresh_token_api: 50/200 - Token刷新
- wechat_login_api: 20/50 - 微信登录
- phone_login_api: 15/40 - 手机号登录

🏦 银行系统 (async_bank_apis):
- check_membership_api: 30/100 - 会员状态检查
- empty_request_api: 50/200 - 空请求处理
- user_goal_api: 40/150 - 用户目标
- activity_list_api: 50/200 - 活动列表
- badhabit_list_api: 40/150 - 坏习惯列表
- user_stats_api: 30/120 - 用户统计
- abstract_goal_list_api: 40/150 - 抽象目标列表
- create_abstract_goal_api: 10/30 - 创建抽象目标
- user_card_month_api: 25/100 - 月度卡片数据
- day_activities_api: 35/140 - 每日活动数据
- check_register_user_api: 20/60 - 用户注册检查

🔍 数据库健康监控 (db_health_api):
- db_pool_stats_api: 60/200 - 连接池统计
- db_reset_pool_api: 5/15 - 重置连接池
- db_health_check_api: 50/180 - 健康检查
- db_handle_timeout_api: 10/30 - 超时处理
- db_async_health_check_api: 50/180 - 异步健康检查
- db_async_reset_pool_api: 5/15 - 异步重置连接池
- db_async_handle_timeout_api: 10/30 - 异步超时处理
- db_start_monitor_api: 5/15 - 启动监控
- db_stop_monitor_api: 5/15 - 停止监控

📚 每日中医题目 (daily_tcm_question_apis):
- daily_tcm_questions_api: 40/150 - 题目查询
- submit_tcm_questions_api: 30/120 - 答题提交
- daily_quiz_score_api: 25/100 - 分数提交
- tcm_categories_api: 50/200 - 分类查询
- question_stats_api: 30/120 - 题目统计
- quiz_ranking_api: 35/140 - 测验排行榜
- user_quiz_history_api: 30/120 - 用户答题历史
- challenge_overview_api: 40/150 - 挑战概览
- challenge_levels_api: 35/140 - 挑战关卡列表
- challenge_level_questions_api: 30/120 - 关卡题目
- submit_challenge_level_api: 20/80 - 挑战提交
- challenge_ranking_api: 35/140 - 挑战排行榜
- challenge_user_history_api: 25/100 - 用户挑战历史
- challenge_stats_api: 30/120 - 挑战统计

🏥 预后系统 (prognosis_api):
- therapy_classifications_api: 40/150 - 疗法分类列表
- create_therapy_classification_api: 5/20 - 创建疗法分类
- therapies_by_classification_api: 35/140 - 分类疗法列表
- all_therapies_api: 40/150 - 所有疗法列表
- therapy_detail_api: 50/200 - 疗法详情
- create_therapy_api: 5/20 - 创建疗法
- create_user_therapy_api: 10/40 - 创建用户疗法
- user_therapies_api: 30/120 - 用户疗法列表
- therapy_comments_api: 40/150 - 疗法评论
- create_therapy_comment_api: 15/50 - 创建评论
- delete_therapy_comment_api: 10/30 - 删除评论
- therapy_like_api: 30/100 - 疗法点赞
- therapy_like_status_api: 50/200 - 点赞状态
- therapy_rating_api: 20/80 - 疗法评分
- therapy_rating_stats_api: 40/150 - 评分统计
- therapy_usage_record_api: 25/100 - 使用记录
- my_therapy_usage_records_api: 30/120 - 我的使用记录
- intervention_record_api: 15/50 - 干预计划
- my_intervention_records_api: 25/100 - 我的干预记录
- user_intervention_records_api: 20/80 - 用户干预记录
- update_intervention_record_api: 20/80 - 更新干预记录
- debug_therapy_usage_api: 10/30 - 调试接口
- therapy_users_intervention_api: 25/100 - 疗法相关记录

🧪 测试API模块:
RouterTest1:
- routertest1_data1_api: 40/150 - 测试数据1
- routertest1_list_api: 35/140 - 测试列表
- routertest1_create_api: 15/50 - 创建测试数据

RouterTest2:
- routertest2_summary_api: 35/140 - 数据摘要
- routertest2_values_api: 40/150 - 数据值
- routertest2_record_api: 20/80 - 添加记录
- routertest2_trends_api: 30/120 - 趋势数据

RouterTest3:
- routertest3_messages_api: 40/150 - 消息列表
- routertest3_send_message_api: 25/100 - 发送消息
- routertest3_mark_read_api: 50/200 - 标记已读
- routertest3_config_api: 30/120 - 配置查询
- routertest3_update_config_api: 15/50 - 配置更新
- routertest3_stats_api: 35/140 - 统计摘要

👤 用户管理:
- user_profile_api: 50/200 - 用户资料
- users_list_api: 25/100 - 用户列表
- user_settings_api: 20/80 - 用户设置

📊 限额配置说明：
格式: normal_limit/member_limit (普通用户每周限额/会员用户每日限额)

🎯 限额设计原则：
1. 查询类接口：相对宽松 (30-60/120-200)
2. 创建/更新类接口：适中限制 (10-25/30-100)
3. 删除/重置类接口：严格限制 (5-15/15-50)
4. 系统管控类接口：最严格限制 (5/15)
5. 聊天类接口：标准限制 (18/100)

⚡ 使用方法：
@rate_limit("api_name", normal_limit=XX, member_limit=YY)
async def your_api_function(request):
    pass

📈 监控和统计：
使用 RateLimitStats 类可以获取用户的API调用统计信息。
""" 