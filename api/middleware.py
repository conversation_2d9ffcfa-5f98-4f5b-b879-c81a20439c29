from django.http import JsonResponse, HttpResponseRedirect
import jwt
from .models.user_models import UserInfo, BannedToken, BannedAccount
from django.conf import settings
from datetime import datetime, timedelta
import logging
import traceback
from django.urls import path, re_path
from asgiref.sync import sync_to_async
from django.db.models import Model
import re
import time
import ipaddress
from django.utils import timezone
from collections import defaultdict
from django.db import models, connection, connections
from django.core.cache import cache
from functools import wraps

# 使用新的异步工具模块
# from api.ninja_apis.async_utils_v2 import get_async, filter_async, save_async
# import asyncio

logger = logging.getLogger('django')

# **添加连接关闭装饰器**
def ensure_db_connection_closed(func):
    """装饰器：确保数据库连接在操作后关闭"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        finally:
            # 强制关闭所有连接
            for conn in connections.all():
                try:
                    conn.close()
                except Exception as e:
                    logger.warning(f"关闭连接时出错: {e}")
    return wrapper

class TokenAuthMiddleware:
    def __init__(self, get_response):
        """初始化中间件"""
        self.get_response = get_response
        # 异常token模式
        self.suspicious_token_patterns = [
            # JWT header被篡改的特征 - 只匹配特定的none算法攻击模式
            r'eyJhbGciOiJub25lIi', 
            # 常见攻击载荷的特征 - 添加边界限定
            r'\beval\(', r'\bexec\(', r'\bsystem\(', r'\b__proto__\b', r'\bconstructor\b', 
            # SQL注入尝试特征 - 更精确的模式匹配
            r'\'\s*or\s+', r';\s*--', r'--\s+', 
            # 异常字符特征
            r'\\x[0-9a-f]{2}', r'\\u[0-9a-f]{4}'
        ]
        # 合法token的起始部分特征 - 用于白名单
        self.valid_token_prefixes = [
            r'eyJhbGciOiJIUzI1NiIs'  # HS256算法的合法前缀
        ]
        # 异常请求计数器，用于记录每个token的异常请求次数
        self.token_request_counter = {}
        # 异常阈值
        self.suspicious_request_threshold = 5
        # 封禁时长（三个月）
        self.ban_duration = timedelta(days=90)
        
        # Token泄露检测相关
        # 记录token的IP访问历史 {token: {ip: last_access_time}}
        self.token_ip_history = defaultdict(dict)
        # 记录token在短时间内的请求次数 {token: [(timestamp, ip), ...]}
        self.token_request_history = defaultdict(list)
        # IP数量阈值 - 一个token被多少个不同IP使用会被视为泄露
        self.ip_threshold = 10  # 修改为5个不同IP
        # 时间窗口 - 在多长时间内计算请求频率（秒）
        self.time_window = 60  # 1分钟
        # 请求频率阈值 - 在时间窗口内超过多少请求会被视为滥用
        self.request_threshold = 200  # 1分钟内60次请求
        # IP变化时间阈值 - 多短时间内IP变化被视为可疑（秒）
        self.ip_change_threshold = 3  # 降低为3秒
        # IP变化计数器 - 允许在一定时间内发生的IP变化次数
        self.allowed_ip_changes = 10  # 允许3次快速IP变化
        # IP变化计数窗口 - 计算IP变化次数的时间窗口（秒）
        self.ip_change_window = 300  # 5分钟内允许self.allowed_ip_changes次IP变化
        # 记录token的IP变化历史 {token: [(change_time, old_ip, new_ip), ...]}
        self.token_ip_changes = defaultdict(list)
        # 清理过期记录的时间间隔（秒）
        self.cleanup_interval = 3600  # 1小时
        self.last_cleanup = time.time()

        # 添加内存缓存，避免频繁数据库查询
        self.banned_token_cache = {}  # token -> (banned_status, expire_time)
        self.cache_expire_time = 300  # 缓存5分钟

    def is_suspicious_token(self, token):
        """检查token是否可疑"""
        # 检查token格式是否正确
        try:
            parts = token.split('.')
            if len(parts) != 3:
                return True, "Token格式不正确"
            
            # 首先检查是否匹配白名单前缀
            for valid_prefix in self.valid_token_prefixes:
                if re.search(valid_prefix, token):
                    # 如果匹配白名单前缀，则认为是合法token
                    return False, ""
                
            # 尝试解码header部分
            try:
                # 不依赖解码结果做决策，只是额外的检查
                import base64
                padding = 4 - (len(parts[0]) % 4)
                if padding < 4:
                    parts[0] += "=" * padding
                header_json = base64.b64decode(parts[0]).decode('utf-8')
                if '"alg":"none"' in header_json:
                    return True, "Token使用了none算法攻击"
            except:
                # 解码失败不影响后续判断
                pass
                
            # 检查token是否包含可疑模式
            for pattern in self.suspicious_token_patterns:
                if re.search(pattern, token):
                    return True, f"Token包含可疑模式: {pattern}"
                    
            return False, ""
        except Exception as e:
            logger.error(f"检查可疑token时出错: {e}")
            return False, ""

    def ban_user(self, user, token, reason="检测到异常token", client_ip=None):
        """封禁用户和相关联的手机号、微信ID"""
        try:
            # 1. 设置用户账号的封禁状态
            user.is_banned = True
            user.ban_reason = reason
            user.ban_time = timezone.now()
            user.ban_until = timezone.now() + self.ban_duration
            user.save()
            
            # 2. 记录被封禁的token
            related_phone = user.wx_phone_new
            related_openid = user.wx_openid_new
            related_unionid = user.wx_unionid_new
            
            # 检查token是否已经被封禁
            existing_ban = BannedToken.objects.filter(token=token).first()
            if existing_ban:
                # 更新现有记录
                existing_ban.user = user
                existing_ban.reason = 'SECURITY'
                existing_ban.detail = f"{reason} | 用户ID: {user.id}, 手机: {related_phone or '未知'}, IP: {client_ip or '未知'}, 时间: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}"
                existing_ban.expire_at = timezone.now() + self.ban_duration
                existing_ban.related_phone = related_phone
                existing_ban.related_openid = related_openid
                existing_ban.ip_address = client_ip
                existing_ban.save()
            else:
                # 创建新记录
                banned_token = BannedToken(
                    token=token,
                    user=user,
                    reason='SECURITY',
                    detail=f"{reason} | 用户ID: {user.id}, 手机: {related_phone or '未知'}, IP: {client_ip or '未知'}, 时间: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    expire_at=timezone.now() + self.ban_duration,
                    is_permanent=False,
                    related_phone=related_phone,
                    related_openid=related_openid,
                    ip_address=client_ip,
                    admin_operator="系统自动"
                )
                banned_token.save()
            
            # 3. 记录手机号和微信ID到BannedAccount表
            # 根据手机号或微信ID查找是否已存在封禁记录
            query = models.Q()
            if related_phone:
                query |= models.Q(phone_number=related_phone)
            if related_openid:
                query |= models.Q(wx_openid=related_openid)
            if related_unionid:
                query |= models.Q(wx_unionid=related_unionid)
            
            existing_ban = None
            if query:
                existing_ban = BannedAccount.objects.filter(query).first()
                
            if existing_ban:
                # 更新现有记录
                existing_ban.reason = 'TOKEN_LEAK' if 'Token泄露' in reason else 'SECURITY'
                existing_ban.detail = reason
                existing_ban.expire_at = timezone.now() + self.ban_duration
                existing_ban.ip_address = client_ip
                existing_ban.ban_count += 1
                existing_ban.user_id = user.id
                existing_ban.save()
            else:
                # 创建新记录
                banned_account = BannedAccount(
                    phone_number=related_phone,
                    wx_openid=related_openid,
                    wx_unionid=related_unionid,
                    user_id=user.id,
                    reason='TOKEN_LEAK' if 'Token泄露' in reason else 'SECURITY',
                    detail=reason,
                    expire_at=timezone.now() + self.ban_duration,
                    is_permanent=False,
                    ip_address=client_ip,
                    admin_operator="系统自动"
                )
                banned_account.save()
            
            logger.warning(f"系统自动封禁用户 ID: {user.id}, 原因: {reason}, IP: {client_ip or '未知'}")
            
            return True
        except Exception as e:
            logger.error(f"封禁用户时出错: {e}")
            return False
            
    def ban_token(self, token, reason="Token滥用", ip=None, user=None):
        """仅封禁token而不封禁用户"""
        try:
            # 查找是否已经存在相同的token记录
            existing_ban = BannedToken.objects.filter(token=token).first()
            if existing_ban:
                # 如果已存在，则更新原因并刷新过期时间
                existing_ban.detail = reason
                existing_ban.expire_at = timezone.now() + self.ban_duration
                if ip:
                    existing_ban.ip_address = ip
                existing_ban.save()
                logger.warning(f"更新封禁Token: {token[:10]}..., 原因: {reason}")
            else:
                # 创建新的封禁记录
                banned_token = BannedToken(
                    token=token,
                    user=user,
                    reason='SECURITY',
                    detail=reason,
                    expire_at=timezone.now() + self.ban_duration,
                    is_permanent=False,
                    ip_address=ip,
                    admin_operator="系统自动"
                )
                banned_token.save()
                logger.warning(f"封禁Token: {token[:10]}..., 原因: {reason}")
            return True
        except Exception as e:
            logger.error(f"封禁Token时出错: {e}")
            return False
            
    def check_token_leak(self, token, ip, request):
        """检查token是否泄露"""
        current_time = time.time()

        # 🚀 检查是否为测试token，如果是则豁免所有检查
        try:
            decoded_token = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'], options={"verify_signature": False})
            if decoded_token.get('test_mode', False):
                logger.info(f"测试Token豁免泄露检查: {token[:10]}...")
                return False, "测试Token豁免"
        except Exception:
            pass  # 如果解码失败，继续正常检查

        # 清理过期记录
        if current_time - self.last_cleanup > self.cleanup_interval:
            self._cleanup_records()
            self.last_cleanup = current_time
            
        # 检查是否是首次使用该token
        is_first_use = token not in self.token_ip_history or not self.token_ip_history[token]
        
        # 检查IP是否发生变化
        ip_changed = False
        last_ip = None
        last_time = 0
        
        if not is_first_use:
            # 找出最近一次访问记录
            history = self.token_request_history.get(token, [])
            if history:
                last_time, last_ip = max([(t, ip) for t, ip in history], key=lambda x: x[0])
                ip_changed = last_ip != ip
        
        # 1. 记录当前IP和访问时间
        self.token_ip_history[token][ip] = current_time
        
        # 2. 记录请求历史
        self.token_request_history[token].append((current_time, ip))
        
        # 3. 检查使用该token的不同IP数量
        unique_ips = len(self.token_ip_history[token])
        if unique_ips >= self.ip_threshold:
            ips = list(self.token_ip_history[token].keys())
            logger.warning(f"Token泄露警告: {token[:10]}... 被多个IP使用: {ips}")
            return True, f"Token被多个IP使用 ({unique_ips}个不同IP)"
        
        # 4. 检查短时间内的IP变化 - 改进的逻辑
        if ip_changed:
            # 记录IP变化
            self.token_ip_changes[token].append((current_time, last_ip, ip))
            
            # 检查是否是快速变化
            if (current_time - last_time) < self.ip_change_threshold:
                # 计算IP变化窗口内的变化次数
                window_start = current_time - self.ip_change_window
                recent_changes = [c for c in self.token_ip_changes[token] if c[0] >= window_start]
                
                # 只有当快速IP变化次数超过允许次数时才判定为泄露
                if len(recent_changes) > self.allowed_ip_changes:
                    change_ips = [f"{old_ip}->{new_ip}" for _, old_ip, new_ip in recent_changes[-5:]]
                    logger.warning(f"Token泄露警告: {token[:10]}... IP频繁变化: {change_ips}")
                    return True, f"Token在{self.ip_change_window/60:.1f}分钟内有{len(recent_changes)}次快速IP变化"
                else:
                    # 记录但不判定为泄露
                    logger.info(f"Token IP变化 ({len(recent_changes)}/{self.allowed_ip_changes}): {token[:10]}... 从{last_ip}变为{ip}")
        
        # 5. 检查短时间内的请求频率
        window_start = current_time - self.time_window
        recent_requests = [r for r in self.token_request_history[token] if r[0] >= window_start]
        if len(recent_requests) > self.request_threshold:
            logger.warning(f"Token滥用警告: {token[:10]}... 在{self.time_window}秒内发出{len(recent_requests)}个请求")
            return True, f"Token在短时间内频繁使用 ({len(recent_requests)}次/分钟)"
        
        # 6. 检查地理位置异常 (需要IP地理位置服务)
        # 如果有IP地理位置服务，可以在这里添加检测逻辑
        
        return False, ""
        
    def _cleanup_records(self):
        """清理过期的记录"""
        current_time = time.time()
        expired_time = current_time - self.time_window * 10  # 保留10个时间窗口的记录
        ip_change_expired_time = current_time - self.ip_change_window * 2  # 保留2个IP变化窗口的记录
        
        # 清理IP历史记录
        for token in list(self.token_ip_history.keys()):
            for ip in list(self.token_ip_history[token].keys()):
                if self.token_ip_history[token][ip] < expired_time:
                    del self.token_ip_history[token][ip]
            if not self.token_ip_history[token]:
                del self.token_ip_history[token]
        
        # 清理请求历史记录
        for token in list(self.token_request_history.keys()):
            self.token_request_history[token] = [r for r in self.token_request_history[token] if r[0] >= expired_time]
            if not self.token_request_history[token]:
                del self.token_request_history[token]
                
        # 清理IP变化记录
        for token in list(self.token_ip_changes.keys()):
            self.token_ip_changes[token] = [c for c in self.token_ip_changes[token] if c[0] >= ip_change_expired_time]
            if not self.token_ip_changes[token]:
                del self.token_ip_changes[token]

    def is_token_banned_cached(self, token):
        """检查token是否被封禁（使用缓存）"""
        current_time = time.time()
        
        # 检查内存缓存
        if token in self.banned_token_cache:
            banned_status, cache_time = self.banned_token_cache[token]
            # 如果缓存未过期
            if current_time - cache_time < self.cache_expire_time:
                return banned_status
            else:
                # 缓存过期，删除
                del self.banned_token_cache[token]
        
        # 检查Django缓存
        cache_key = f"banned_token_{hash(token) % 10000}"  # 使用hash避免key过长
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            self.banned_token_cache[token] = (cached_result, current_time)
            return cached_result
        
        # 缓存未命中，返回None表示需要查询数据库
        return None

    def cache_token_status(self, token, is_banned):
        """缓存token状态"""
        current_time = time.time()
        
        # 更新内存缓存
        self.banned_token_cache[token] = (is_banned, current_time)
        
        # 更新Django缓存
        cache_key = f"banned_token_{hash(token) % 10000}"
        cache.set(cache_key, is_banned, timeout=self.cache_expire_time)

    @ensure_db_connection_closed
    def is_token_banned_sync(self, token):
        """同步检查token是否被封禁（带缓存）"""
        # 先检查缓存
        cached_result = self.is_token_banned_cached(token)
        if cached_result is not None:
            return cached_result  # 返回缓存的结果（True/False）
        
        # 缓存未命中，查询数据库
        try:
            banned_token = BannedToken.objects.filter(token=token).first()
            is_banned = banned_token is not None and not banned_token.is_expired()
            
            # 缓存结果
            self.cache_token_status(token, is_banned)
            
            return banned_token if is_banned else None
        except Exception as e:
            logger.error(f"检查token封禁状态时出错: {e}")
            # 出错时缓存为False，避免重复查询
            self.cache_token_status(token, False)
            return None

    @ensure_db_connection_closed  
    def _get_user_by_id(self, user_id):
        """获取用户信息（带连接关闭）"""
        try:
            return UserInfo.objects.get(id=user_id)
        except UserInfo.DoesNotExist:
            return None
    
    @ensure_db_connection_closed
    def _save_user(self, user):
        """保存用户信息（带连接关闭）"""
        user.save()
    
    @ensure_db_connection_closed
    def _save_banned_token(self, banned_token):
        """保存封禁token（带连接关闭）"""
        banned_token.save()

    @ensure_db_connection_closed
    def _check_banned_account(self, phone, openid, unionid):
        """检查封禁账号（带连接关闭）"""
        return BannedAccount.check_banned(phone=phone, openid=openid, unionid=unionid)

    def __call__(self, request):
        """每个请求都会调用此方法"""
        start_time = time.time()
        
        # 跳过不需要认证的路径
        if (request.path.startswith('/api/bank/user-create/') or
            request.path.startswith('/api/bank/token-obtain/') or
            request.path.startswith('/api/bank/token-refresh/') or
            request.path.startswith('/api/HomePage') or
            request.path.startswith('/api/test/register_or_login_by_phone/') or
            request.path.startswith('/api/admineee-riyuetcm-123/') or
            request.path.startswith('/api/bank/DailyTCMQuestionView/') or
            request.path.startswith('/api/check_static/') or
            request.path.startswith('/api/terms/') or
            request.path.startswith('/api/privacy/') or
            request.path.startswith('/api/contact/') or
            request.path.startswith('/api/test/wechat-login11/') or
            request.path.startswith('/api/bank/refresh-token/') or 
            request.path.startswith('/api/auth/refresh-token') or  # 新的异步refresh-token API
            request.path.startswith('/api/auth/wechat-login') or  # 新的异步微信登录API
            request.path.startswith('/api/auth/phone-login') or   # 新的异步手机号登录API
            request.path.startswith('/api/tcmNLP/AlipayAuthCallbackView/') or
            request.path.startswith('/api/tcmNLP/CheckPaymentStatusView/') or
            request.path.startswith('/api/test/register_or_login_by_phone/') or
            request.path.startswith('/api/wechatV3_notify/') or
            request.path.startswith('/api/ws/test/') or
            request.path.startswith('/api/db-pool-monitor/') or
            request.path.startswith('/api/db-health/health-check') or
            request.path.startswith('/api/db-health/pool-stats') or
            request.path == '/' or
            request.path.startswith('/api/tcmNLP/callback/')):
            return self.get_response(request)

        # 获取客户端IP
        client_ip = self._get_client_ip(request)

        # 检查Authorization头
        auth_check_start = time.time()
        auth_header = request.headers.get('Authorization', '')
        
        if not auth_header:
            return JsonResponse({'error': '访问需要权限'}, status=409)

        if not auth_header.startswith('Bearer '):
            return JsonResponse({'error': '访问需要权限'}, status=402)

        # 从Bearer后提取token
        token = auth_header[7:]  # 移除前缀 "Bearer "
        if not token:
            return JsonResponse({'error': '访问需要权限'}, status=403)

        # 检查token是否泄露或被滥用
        is_leaked, leak_reason = self.check_token_leak(token, client_ip, request)
        if is_leaked:
            # 修改为：泄露token时直接封禁用户账号
            try:
                # 尝试解码token获取用户ID
                decoded_token = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'], options={"verify_signature": False})
                user_id = decoded_token.get('user_id')
                
                if user_id:
                    try:
                        user = self._get_user_by_id(user_id)
                        if user:
                            # 封禁用户
                            self.ban_user(user, token, f"Token泄露或滥用: {leak_reason}", client_ip)
                            return JsonResponse({'error': '您的账号已因Token泄露被封禁三个月', 'detail': leak_reason}, status=423)
                    except Exception:
                        pass
            except:
                # 如果解码失败，仅封禁token
                self.ban_token(token, f"Token泄露或滥用: {leak_reason}", ip=client_ip)
            
            return JsonResponse({'error': 'Token已被系统封禁，账号访问受限', 'detail': leak_reason}, status=423)

        # 🚀 检查是否为测试token，如果是则豁免异常检查
        try:
            decoded_token = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'], options={"verify_signature": False})
            if decoded_token.get('test_mode', False):
                logger.info(f"测试Token豁免异常检查: {token[:10]}...")
                # 测试token跳过所有异常检查，直接进入正常验证流程
                pass
            else:
                # 检查token是否可疑
                is_suspicious, reason = self.is_suspicious_token(token)
                if is_suspicious:
                    # 记录可疑请求
                    self.token_request_counter[token] = self.token_request_counter.get(token, 0) + 1
                    logger.warning(f"检测到可疑token: {token[:10]}..., 原因: {reason}, 次数: {self.token_request_counter[token]}")
        except Exception:
            # 如果解码失败，继续正常的异常检查
            is_suspicious, reason = self.is_suspicious_token(token)
            if is_suspicious:
                # 记录可疑请求
                self.token_request_counter[token] = self.token_request_counter.get(token, 0) + 1
                logger.warning(f"检测到可疑token: {token[:10]}..., 原因: {reason}, 次数: {self.token_request_counter[token]}")
            
            # 如果超过阈值，直接拒绝请求并封禁
            if self.token_request_counter[token] >= self.suspicious_request_threshold:
                try:
                    # 尝试解码token获取用户ID
                    decoded_token = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'], options={"verify_signature": False})
                    user_id = decoded_token.get('user_id')
                    
                    if user_id:
                        try:
                            user = self._get_user_by_id(user_id)
                            if user:
                                # 封禁用户
                                self.ban_user(user, token, f"检测到异常token: {reason}", client_ip)
                                return JsonResponse({'error': '您的账号已因异常行为被封禁三个月'}, status=423)
                        except Exception:
                            pass
                except:
                    # 如果解码失败，记录token到封禁列表但不关联用户
                    banned_token = BannedToken(
                        token=token,
                        reason='SECURITY',
                        detail=f"异常token: {reason}",
                        expire_at=timezone.now() + self.ban_duration,
                        is_permanent=False,
                        admin_operator="系统自动"
                    )
                    self._save_banned_token(banned_token)
                    return JsonResponse({'error': '检测到异常访问，已被系统拒绝'}, status=423)

        # 使用缓存优化的方式检查token是否在封禁列表中
        try:
            # 先检查缓存
            cached_result = self.is_token_banned_cached(token)
            if cached_result is True:
                # 缓存显示已封禁，直接返回
                logger.warning(f"缓存显示封禁Token尝试访问: {token[:10]}...")
                return JsonResponse({
                    'error': '您的账号已被封禁',
                    'reason': '安全威胁',
                    'detail': '该Token已被系统封禁'
                }, status=423)
            elif cached_result is False:
                # 缓存显示未封禁，跳过数据库查询
                pass
            else:
                # 缓存未命中，需要查询数据库 - 使用同步查询
                banned_token = self.is_token_banned_sync(token)
                if banned_token:
                    # 记录封禁尝试
                    logger.warning(f"封禁Token尝试访问: {token[:10]}...")
                    return JsonResponse({
                        'error': '您的账号已被封禁',
                        'reason': banned_token.get_reason_display(),
                        'detail': banned_token.detail,
                        'expire_at': banned_token.expire_at.isoformat() if banned_token.expire_at else None,
                        'is_permanent': banned_token.is_permanent
                    }, status=423)
        except Exception as e:
            logger.error(f"检查token封禁状态时出错: {e}")
            # 这里不返回错误，继续处理，确保服务可用性

        try:
            # JWT解码计时
            jwt_start = time.time()
            decoded_token = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
            user_id = decoded_token.get('user_id')
            
            # 检查是否为IP受限的测试token
            if decoded_token.get('ip_restricted', False):
                allowed_ips = decoded_token.get('allowed_ips', [])
                if not self._is_test_token_ip_allowed(client_ip, allowed_ips):
                    logger.warning(f"测试Token IP限制: 客户端IP {client_ip} 不在允许列表中: {allowed_ips}")
                    return JsonResponse({
                        'error': '测试Token仅限指定IP访问',
                        'detail': f'当前IP {client_ip} 不在允许列表中',
                        'allowed_ips': allowed_ips
                    }, status=423)

            try:
                # 数据库操作计时
                db_start = time.time()
                user = self._get_user_by_id(user_id)
                if not user:
                    return JsonResponse({'error': '用户不存在'}, status=405)
                
                # 检查用户账号是否已被封禁
                if user.is_banned:
                    ban_info = {
                        'error': '您的账号已被封禁',
                        'reason': user.ban_reason,
                        'ban_time': user.ban_time.isoformat() if user.ban_time else None,
                    }
                    
                    # 如果有封禁截止时间，检查是否已过期
                    if user.ban_until:
                        if user.ban_until > timezone.now():
                            ban_info['ban_until'] = user.ban_until.isoformat()
                        else:
                            # 封禁已过期，解除封禁
                            user.is_banned = False
                            self._save_user(user)
                            # 继续正常流程
                    else:
                        # 没有截止时间，返回封禁消息
                        return JsonResponse(ban_info, status=423)
                
                # 检查手机号和微信ID是否被封禁（处理注销后重新注册的情况）
                is_banned, banned_info = self._check_banned_account(
                    phone=user.wx_phone_new, 
                    openid=user.wx_openid_new,
                    unionid=user.wx_unionid_new
                )
                
                if is_banned:
                    # 如果检测到此用户的手机号或微信ID在封禁列表中，自动封禁当前账号
                    if not user.is_banned:
                        user.is_banned = True
                        user.ban_reason = f"关联账号被封禁: {banned_info.get_reason_display()}"
                        user.ban_time = timezone.now()
                        user.ban_until = banned_info.expire_at
                        self._save_user(user)
                    
                    return JsonResponse({
                        'error': '您的账号已被封禁',
                        'reason': banned_info.get_reason_display(),
                        'detail': banned_info.detail,
                        'expire_at': banned_info.expire_at.isoformat() if banned_info.expire_at else None
                    }, status=423)
                
                # 正常流程继续
            except Exception as e:
                logger.error(f"获取用户信息时出错: {e}")
                return JsonResponse({'error': '用户验证失败'}, status=500)

            # 设置request.user_id并处理请求
            request.user_id = user_id
            response_start = time.time()
            response = self.get_response(request)
            
            total_time = time.time() - start_time
            return response

        except jwt.ExpiredSignatureError:
            return JsonResponse({'error': 'Token已过期，请重新登录'}, status=407)
        except jwt.InvalidTokenError:
            # 记录无效token可能是攻击尝试
            logger.warning(f"无效的Token: {token[:10]}...")
            
            # 记录token到可疑计数中
            self.token_request_counter[token] = self.token_request_counter.get(token, 0) + 1
            if self.token_request_counter[token] >= self.suspicious_request_threshold:
                # 将token加入封禁列表
                banned_token = BannedToken(
                    token=token,
                    reason='SECURITY',
                    detail="多次使用无效Token尝试访问",
                    expire_at=timezone.now() + self.ban_duration,
                    is_permanent=False,
                    admin_operator="系统自动"
                )
                self._save_banned_token(banned_token)
            
            logger.info("错误：无效的 Token")
            return JsonResponse({'error': '访问需要权限'}, status=406)
        except Exception as e:
            logger.error(f"未处理的异常: {e}")
            return JsonResponse({'error': '访问需要权限2'}, status=407)
            
    def _get_client_ip(self, request):
        """获取客户端真实IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '')
        return ip
    
    def _is_test_token_ip_allowed(self, client_ip: str, allowed_ips: list) -> bool:
        """检查测试token的IP是否在允许列表中"""
        if not client_ip or not allowed_ips:
            return False
        
        try:
            import ipaddress
            client_ip_obj = ipaddress.ip_address(client_ip)
            
            for allowed_ip in allowed_ips:
                try:
                    # 处理域名形式
                    if allowed_ip == 'localhost' and client_ip in ['127.0.0.1', '::1']:
                        return True
                    
                    # 处理CIDR网络段
                    if '/' in allowed_ip:
                        network = ipaddress.ip_network(allowed_ip, strict=False)
                        if client_ip_obj in network:
                            return True
                    else:
                        # 处理单个IP地址
                        if client_ip_obj == ipaddress.ip_address(allowed_ip):
                            return True
                except (ipaddress.AddressValueError, ValueError):
                    # 如果IP格式不正确，跳过
                    continue
            
            return False
        except (ipaddress.AddressValueError, ValueError):
            # 客户端IP格式不正确
            return False

# 添加一个全局异常捕获中间件
class ExceptionLoggingMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        # 获取日志记录器
        self.logger = logging.getLogger('django')  # 或者使用 'api' 如果你为api配置了单独的logger

    def __call__(self, request):
        try:
            # 调用下一个中间件或视图
            response = self.get_response(request)
            return response
        except Exception as e:
            # 使用 logger.exception 来记录错误和堆栈
            self.logger.exception("=================== 全局异常捕获 ===================")
            self.logger.error(f"路径: {request.path}")
            self.logger.error(f"方法: {request.method}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"错误信息: {str(e)}")
            self.logger.error("详细堆栈已记录在上一条 exception 日志中")
            self.logger.error("====================================================")
            
            # 对于API请求，返回JSON格式的错误
            if '/api/' in request.path:
                return JsonResponse({
                    'status': 'error',
                    'message': f'服务器内部错误，请联系管理员。',
                    'error_type': type(e).__name__,  # 可以选择不暴露具体错误类型给前端
                    'path': request.path
                }, status=500)
            
            # 对于非API请求，可以选择重新抛出让Django处理，或返回通用错误页面
            # 重新抛出异常，让Django的默认处理器处理 (适用于非API视图)
            # raise 
            # 或者返回一个通用的错误响应
            from django.http import HttpResponseServerError
            return HttpResponseServerError("服务器内部错误，请稍后重试。")