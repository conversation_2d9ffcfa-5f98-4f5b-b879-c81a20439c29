# api/ninja_apis/__init__.py
# 🔒 Django Ninja API 统一管理模块 - 防重复导入版本
# 
# 【重要说明】Django启动时会多次导入URL配置模块，包括：
# 1. 系统检查阶段 - Django验证URL配置的有效性
# 2. 自动重载监听 - 开发模式下监听文件变化
# 3. URL模式构建 - 构建完整的URL路由映射
# 4. 错误恢复导入 - 当导入出错时Django会尝试重新导入
#
# 为防止路由重复注册导致 "Router@'' has already been attached" 错误，
# 本模块采用【单例初始化模式】确保所有路由只注册一次。

from ninja import NinjaAPI
from api.views import tcmNLP, tcmchat, invite_views  # 移除ninja_chat的导入
from .questionnaire_api import questionnaire_router
from .db_health_api import db_health_router
from .bazi_analysis_api import bazi_analysis_router
from .auth_api import auth_router
from .async_bank_apis import async_bank_router

# 🆕 新增的3个测试API模块导入 - 无意义命名避免误会
from .routertest1.test_api import routertest1_router
from .routertest1.prognosis_api import prognosis_router
from .routertest2.test_api import routertest2_router
from .routertest3.test_api import routertest3_router

# 🔮 八字分析API模块导入 - 集成到routertest1
# 注意：八字分析API直接使用routertest1_router，通过导入来激活路由注册
from .routertest1 import bazi_analysis_api  # 导入以激活八字分析API路由

# 🚀 性能测试API模块导入 - 集成到routertest1
# 注意：性能测试API直接使用routertest1_router，通过导入来激活路由注册
from .routertest1 import performance_test_api  # 导入以激活性能测试API路由

# ===============================================
# 🛡️ 单例初始化控制系统
# ===============================================
# 全局初始化状态标记 - 防止重复初始化的核心机制
_initialized = False
_initialization_lock = False  # 防止并发初始化的锁

# 初始化日志列表 - 用于调试和监控初始化过程
_init_log = []

def _log_init(message: str):
    """记录初始化日志 - 用于调试重复导入问题"""
    _init_log.append(f"[NINJA_INIT] {message}")
    print(f"✅ [NINJA_INIT] {message}")

# ===============================================
# 🏗️ API实例创建区域 
# ===============================================
# 注意：API实例的创建是安全的，可以重复执行
# 但路由注册必须只执行一次，否则会报错
_log_init("开始创建API实例...")

# 🔥 创建API实例 - 确保每个API都有完全唯一的版本号和命名空间
bank_api = NinjaAPI(version="1.0", urls_namespace="bank_api")
doubao_aichat = NinjaAPI(version="2.0", urls_namespace="doubao_aichat_api")
invite_api = NinjaAPI(version="3.0", urls_namespace="invite_api")
questionnaire_api = NinjaAPI(version="4.0", title="问卷系统API", urls_namespace="questionnaire_api")
# 🎯 修复db_health_api配置冲突 - 使用更独特的命名空间
db_health_api = NinjaAPI(version="5.0", title="数据库健康监控API", urls_namespace="db_health_monitor_api")
auth_api = NinjaAPI(version="6.0", title="认证API", urls_namespace="auth_api")
# 新增异步银行API - 用于替代传统Django View，解决连接池泄漏
async_bank_api = NinjaAPI(version="7.0", title="异步银行API", urls_namespace="async_bank_api")
# 八字分析API - 提示词管理和分析类型查询
bazi_analysis_api = NinjaAPI(version="11.0", title="八字分析API", urls_namespace="bazi_analysis_api")

# 🆕 新增的3个测试API实例 - 无意义命名避免误会
routertest1_api = NinjaAPI(version="8.0", title="RouterTest1 API", urls_namespace="routertest1_api")
routertest2_api = NinjaAPI(version="9.0", title="RouterTest2 API", urls_namespace="routertest2_api") 
routertest3_api = NinjaAPI(version="10.0", title="RouterTest3 API", urls_namespace="routertest3_api")

_log_init("API实例创建完成 - 包括3个测试API模块 (routertest1-3)")

# ===============================================
# 🔧 路由注册函数区域
# ===============================================

def _register_core_routes():
    """注册核心业务路由 - 问卷、数据库健康、认证、异步银行"""
    try:
        # 问卷系统路由
        questionnaire_api.add_router("", questionnaire_router)
        _log_init("✅ 问卷系统路由注册成功")
        
        # 数据库健康监控路由
        db_health_api.add_router("", db_health_router)
        _log_init("✅ 数据库健康监控路由注册成功")
        
        # 认证系统路由
        auth_api.add_router("", auth_router)
        _log_init("✅ 认证系统路由注册成功")
        
        # 异步银行API路由
        async_bank_api.add_router("", async_bank_router)
        _log_init("✅ 异步银行API路由注册成功")

        # 八字分析API路由
        bazi_analysis_api.add_router("", bazi_analysis_router)
        _log_init("✅ 八字分析API路由注册成功")

        # 邀请系统路由
        invite_api.add_router("/invite_api/", invite_views.router)
        _log_init("✅ 邀请系统路由注册成功")
        
    except Exception as e:
        _log_init(f"❌ 核心路由注册失败: {str(e)}")
        # 不抛出异常，允许其他路由继续注册
        return False
    
    return True

def _register_new_api_routes():
    """🆕 注册新增的3个测试API模块路由 - routertest1、routertest2、routertest3"""
    try:
        # RouterTest1 API路由
        routertest1_api.add_router("", routertest1_router)
        _log_init("✅ RouterTest1 API路由注册成功")
        
        # 🏥 Prognosis API路由 - 集成到RouterTest1中进行测试
        routertest1_api.add_router("/prognosis", prognosis_router)
        _log_init("✅ Prognosis API路由注册成功 (集成到RouterTest1)")
        
        # RouterTest2 API路由  
        routertest2_api.add_router("", routertest2_router)
        _log_init("✅ RouterTest2 API路由注册成功")
        
        # RouterTest3 API路由
        routertest3_api.add_router("", routertest3_router)
        _log_init("✅ RouterTest3 API路由注册成功")
        
    except Exception as e:
        _log_init(f"❌ 测试API路由注册失败: {str(e)}")
        return False
    
    return True

def _register_chat_routes():
    """注册聊天系统路由 - 独立处理避免循环导入"""
    try:
        # 尝试导入聊天路由（可能存在循环导入风险）
        from api.views import ninja_chat
        
        # 只添加不包含预后系统的路由
        doubao_aichat.add_router("/chat/", ninja_chat.router)
        _log_init("✅ 聊天系统路由注册成功")
        
    except ImportError as e:
        _log_init(f"⚠️ 聊天路由导入失败: {str(e)} - 使用占位路由")
        # 创建占位路由确保API可用
        _create_chat_placeholder()
        return False
        
    except Exception as e:
        _log_init(f"❌ 聊天路由注册失败: {str(e)} - 使用占位路由")
        _create_chat_placeholder()
        return False
    
    return True

def _create_chat_placeholder():
    """创建聊天系统占位路由 - 当主路由不可用时使用"""
    try:
        from ninja import Router as PlaceholderRouter
        placeholder_router = PlaceholderRouter()
        
        @placeholder_router.get("/test")
        def placeholder_test(request):
            return {"message": "doubao_aichat API正常工作（占位模式）", "status": "ok"}
        
        doubao_aichat.add_router("/chat/", placeholder_router)
        _log_init("✅ 聊天系统占位路由创建成功")
        
    except Exception as e:
        _log_init(f"❌ 占位路由创建失败: {str(e)}")

def _register_prognosis_routes():
    """注册预后系统路由 - 安全导入避免循环依赖"""
    try:
        # 🎯 预后系统现已集成到db_health_api，无需单独注册
        # 这里保留函数以防未来需要扩展
        _log_init("✅ 预后系统路由已集成到db_health_api")
        return True
        
    except Exception as e:
        _log_init(f"⚠️ 预后路由处理: {str(e)}")
        return False

# ===============================================
# 🚀 主初始化函数
# ===============================================

def initialize_ninja_apis():
    """
    主初始化函数 - 采用单例模式确保只执行一次
    
    【防重复导入机制说明】：
    1. 使用全局变量 _initialized 跟踪初始化状态
    2. 使用 _initialization_lock 防止并发初始化
    3. 分模块注册路由，单个模块失败不影响其他模块
    4. 详细的日志记录便于调试问题
    5. 异常处理确保初始化过程的健壮性
    """
    global _initialized, _initialization_lock
    
    # 🔒 检查是否已经初始化
    if _initialized:
        _log_init("⏭️ API已初始化，跳过重复初始化")
        return True
    
    # 🔒 检查是否正在初始化（防止并发）
    if _initialization_lock:
        _log_init("⏳ API正在初始化中，等待完成...")
        return False
    
    # 🔒 设置初始化锁
    _initialization_lock = True
    _log_init("🔧 开始初始化Django Ninja APIs...")
    
    try:
        # 步骤1: 注册核心业务路由
        core_success = _register_core_routes()
        
        # 步骤2: 注册新增的测试API路由
        new_api_success = _register_new_api_routes()
        
        # 步骤3: 注册聊天系统路由
        chat_success = _register_chat_routes()
        
        # 步骤4: 处理预后系统路由
        prognosis_success = _register_prognosis_routes()
        
        # 步骤5: 标记初始化完成
        _initialized = True
        _log_init("🎉 所有API路由初始化完成！")
        
        # 步骤6: 输出初始化摘要
        success_count = sum([core_success, new_api_success, chat_success, prognosis_success])
        _log_init(f"📊 初始化摘要: 成功 {success_count}/4 个模块组")
        _log_init(f"🆕 测试模块: routertest1、routertest2、routertest3")
        
        return True
        
    except Exception as e:
        _log_init(f"💥 初始化过程出现未预期错误: {str(e)}")
        # 即使出错也标记为已初始化，避免无限重试
        _initialized = True
        return False
        
    finally:
        # 🔓 释放初始化锁
        _initialization_lock = False

# ===============================================
# 🎯 模块导入时自动初始化
# ===============================================

# 立即执行初始化 - 但有防重复保护
initialize_ninja_apis()

# ===============================================
# 🔍 调试和监控工具
# ===============================================

def get_init_status():
    """获取初始化状态 - 用于调试"""
    return {
        "initialized": _initialized,
        "initialization_lock": _initialization_lock,
        "init_log": _init_log,
        "api_count": 10,  # 当前定义的API数量（包括新增的3个）
        "test_apis": ["routertest1_api", "routertest2_api", "routertest3_api"]
    }

def reset_initialization():
    """重置初始化状态 - 仅用于测试"""
    global _initialized, _initialization_lock
    _initialized = False
    _initialization_lock = False
    _init_log.clear()
    _log_init("🔄 初始化状态已重置")

# ===============================================
# 📖 如何添加新API模块的完整指南
# ===============================================

"""
🚀 【如何添加新的API模块】- 防重复导入最佳实践指南

以下是添加新API模块的标准流程，确保不会出现重复导入问题：

📁 1. 创建模块文件夹结构：
   mkdir -p api/ninja_apis/your_module_name
   touch api/ninja_apis/your_module_name/__init__.py
   touch api/ninja_apis/your_module_name/your_api.py

📝 2. 编写API模块代码 (your_api.py)：
   ```python
   from ninja import Router
   
   # 使用Router而非NinjaAPI - 这是关键！
   your_module_router = Router()
   
   @your_module_router.get("/endpoint")
   async def your_endpoint(request):
       return {"status": "success", "message": "新模块工作正常"}
   ```

🔧 3. 在本文件中添加导入（第16行附近）：
   ```python
   from .your_module_name.your_api import your_module_router
   ```

🏗️ 4. 在本文件中创建API实例（第58行附近）：
   ```python
   your_module_api = NinjaAPI(
       version="X.0", 
       title="你的模块API", 
       urls_namespace="your_module_api"
   )
   ```

⚙️ 5. 在_register_new_api_routes函数中添加注册（第88行附近）：
   ```python
   your_module_api.add_router("", your_module_router)
   _log_init("✅ 你的模块API路由注册成功")
   ```

🌐 6. 在api/urls.py中添加URL配置：
   ```python
   from .ninja_apis import ..., your_module_api
   
   urlpatterns = [
       ...
       path("your-module/", your_module_api.urls),
   ]
   ```

✅ 7. 测试新模块：
   python manage.py check
   curl http://localhost:8000/api/your-module/endpoint

🔑 关键注意事项：
- ✅ 使用Router而非NinjaAPI实例
- ✅ 在主__init__.py中统一管理注册
- ✅ 每个API实例使用唯一的版本号和命名空间
- ✅ 异常处理确保单个模块失败不影响其他模块
- ✅ 遵循异步编程模式（async def）

❌ 避免的错误：
- ❌ 在模块文件中直接创建NinjaAPI实例
- ❌ 在模块文件中直接调用add_router
- ❌ 重复使用相同的命名空间
- ❌ 忘记在urls.py中添加路由
"""

# ===============================================
# 📝 模块完成标记
# ===============================================

_log_init("📦 api/ninja_apis/__init__.py 模块加载完成（包含测试API功能）")

# 🎯 重要提示：
# 1. 此文件采用单例初始化模式，确保路由只注册一次
# 2. 即使Django多次导入此模块，也不会重复注册路由  
# 3. 如需调试初始化过程，可调用 get_init_status() 查看状态
# 4. 所有API实例已导出，可在其他模块中安全使用
# 5. 新增了3个测试API模块：routertest1、routertest2、routertest3（无意义命名避免误会）
# 6. 参考上方指南添加更多API模块 