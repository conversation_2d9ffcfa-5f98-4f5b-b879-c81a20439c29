# api/ninja_apis/routertest1/prognosis_api.py
# 🏥 Prognosis Therapy API模块 - 防重复导入测试版本
#
# 【重要说明】本模块遵循防重复导入最佳实践：
# 1. 路由定义与注册分离
# 2. 使用Router而非直接的NinjaAPI实例
# 3. 在主__init__.py中统一管理注册
# 4. 只包含therapy相关的API，不包含症状和体质管理

from ninja import Router
from ninja.errors import HttpError
from typing import List, Dict, Any
import asyncio
import json
import traceback
from django.core.cache import cache
from django.db.models import Count, Q
from asgiref.sync import sync_to_async

# 导入schemas
from api.views.schemas import (
    PrognosisTherapyCategorySchema, PrognosisTherapyCategoryDetailSchema, PrognosisTherapyCategoryIn,
    PrognosisTherapyClassificationSchema, PrognosisTherapyClassificationIn,
    PrognosisTherapyLikeSchema,
    PrognosisTherapyRatingIn, PrognosisTherapyRatingSchema,
    PrognosisTherapyCommentIn, PrognosisTherapyCommentSchema,
    PrognosisTherapyCommentListOut,
    TherapyRecommendationIn, TherapyRecommendationListOut,
    UserTherapyUsageSchema,
    TherapyUsageRecordIn,
    EffectivenessAnalysisSchema,
    PrognosisUserTherapyIn, PrognosisUserTherapySchema,
    # 干预记录相关schemas
    InterventionRecordIn, InterventionRecordUpdateIn, InterventionRecordSchema,
    InterventionRecordCompleteIn, TherapyStatisticsSchema, PersonalEffectivenessSchema,
    TherapyByClassificationSchema
)

# 导入模型
from api.models import (
    PrognosisTherapyClassification, 
    PrognosisTherapyCategory, 
    PrognosisUserTherapy
)

# 导入工具函数
from api.ninja_apis.async_utils import filter_async, get_async, save_async, count_async
from api.ninja_apis.questionnaire.utils import api_timer
# 导入限流装饰器
from api.utils.rate_limiter import rate_limit
# 导入专用缓存装饰器
from .cache_decorators import (
    classification_cache, therapy_list_cache, therapy_detail_cache, 
    user_therapy_cache, CacheTimeout
)

# ===============================================
# 🛡️ 创建路由器实例 - 注意：这里用Router，不是NinjaAPI
# ===============================================
prognosis_router = Router()

# ===============================================
# 📝 疗法分类管理相关的API端点
# ===============================================

@prognosis_router.get("/therapy-classifications", response=List[PrognosisTherapyClassificationSchema])
@classification_cache(timeout=CacheTimeout.LONG)  # 🎯 使用专用缓存装饰器
@api_timer("获取疗法分类列表")
@rate_limit("therapy_classifications_api", normal_limit=40, member_limit=150)  # 分类查询相对频繁
async def list_therapy_classifications(request, category_type: str = None):
    """
    获取所有疗法分类 - 高性能缓存版本，支持分类类型过滤
    
    【测试URL】: /api/routertest1/prognosis/therapy-classifications?category_type=observe
    
    参数说明:
    - category_type: 分类类型过滤 (可选: observe - 观察类分类, therapy - 疗法类分类)
    """
    try:
        print(f"[DEBUG] 开始处理疗法分类请求 - user_id: {getattr(request, 'user_id', 'unknown')}, category_type: {category_type}")
        
        # 🎯 根据category_type构建查询条件
        if category_type == 'observe':
            # 观察类分类：以 _observe 结尾的分类
            print(f"[DEBUG] 过滤观察类分类（以_observe结尾）")
            classifications = await filter_async(
                PrognosisTherapyClassification,
                is_active=True,
                code__endswith='_observe'
            )
        elif category_type == 'therapy':
            # 疗法类分类：不以 _observe 结尾的分类
            print(f"[DEBUG] 过滤疗法类分类（不以_observe结尾）")
            # 使用异步工具函数，先获取所有活跃分类，然后在Python层面过滤
            all_classifications = await filter_async(
                PrognosisTherapyClassification,
                is_active=True
            )
            classifications = [cls for cls in all_classifications if not cls.code.endswith('_observe')]
        else:
            # 默认：所有分类
            print(f"[DEBUG] 获取所有分类（无类型过滤）")
            classifications = await filter_async(
                PrognosisTherapyClassification,
                is_active=True
            )
        
        print(f"[DEBUG] 查询到疗法分类数量: {len(classifications)}")
        
        # 批量统计系统疗法数量 - 使用异步工具函数
        # 先获取所有活跃的疗法分类，然后在Python层面统计
        all_therapies = await filter_async(
            PrognosisTherapyCategory,
            is_active=True,
            classification__isnull=False
        )
        system_therapy_stats = {}
        for therapy in all_therapies:
            classification_id = therapy.classification_id
            if classification_id:
                system_therapy_stats[classification_id] = system_therapy_stats.get(classification_id, 0) + 1
        
        # 批量统计用户疗法数量 - 使用异步工具函数
        user_therapy_stats = {}
        try:
            classification_codes = [cls.code for cls in classifications]
            # 获取所有用户疗法，然后在Python层面统计
            all_user_therapies = await filter_async(
                PrognosisUserTherapy,
                category__in=classification_codes
            )

            # 将code映射回id
            code_to_id = {cls.code: cls.id for cls in classifications}
            code_stats = {}
            for therapy in all_user_therapies:
                category = therapy.category
                if category:
                    code_stats[category] = code_stats.get(category, 0) + 1

            # 转换为id统计
            for code, count in code_stats.items():
                if code in code_to_id:
                    user_therapy_stats[code_to_id[code]] = count
        except Exception as e:
            print(f"[WARNING] 统计用户疗法数量失败: {e}")
            user_therapy_stats = {}
        
        print(f"[DEBUG] 系统疗法统计完成，涉及分类数: {len(system_therapy_stats)}")
        print(f"[DEBUG] 用户疗法统计完成，涉及分类数: {len(user_therapy_stats)}")
        
        # 构建结果 - 使用异步方法获取准确的疗法数量
        result = []
        for classification in classifications:
            # 🚀 使用异步方法获取准确的疗法数量，而不是缓存统计
            try:
                therapy_count = await classification.async_therapy_count()
                print(f"[DEBUG] 异步获取分类 {classification.name} 疗法数量: {therapy_count}")
            except Exception as e:
                print(f"[WARNING] 异步获取分类疗法数量失败: {e}")
                # 回退到原来的统计方法
                system_count = system_therapy_stats.get(classification.id, 0)
                user_count = user_therapy_stats.get(classification.id, 0)
                therapy_count = system_count + user_count
            
            classification_data = {
                'id': classification.id,
                'name': classification.name,
                'code': classification.code,
                'description': classification.description,
                'icon': classification.icon,
                'color': classification.color,
                'sort_order': classification.sort_order,
                'is_active': classification.is_active,
                'created_at': classification.created_at,
                'updated_at': classification.updated_at,
                'therapy_count': therapy_count  # 🚀 真实的异步统计数据
            }
            result.append(classification_data)
        
        # 按sort_order排序
        result.sort(key=lambda x: x['sort_order'])
        
        # 🎯 打印过滤结果统计
        if category_type == 'observe':
            observe_count = len([r for r in result if r['code'].endswith('_observe')])
            print(f"[DEBUG] 观察类分类过滤结果: {observe_count}/{len(result)} 个分类")
        elif category_type == 'therapy':
            therapy_count = len([r for r in result if not r['code'].endswith('_observe')])
            print(f"[DEBUG] 疗法类分类过滤结果: {therapy_count}/{len(result)} 个分类")
        
        print(f"[DEBUG] 疗法分类API处理完成，返回数据数量: {len(result)}")
        print(f"[DEBUG] 返回的分类统计:")
        for cls in result:
            print(f"[DEBUG]   - {cls['name']} ({cls['code']}): {cls.get('therapy_count', 0)} 个疗法")
        
        return result
        
    except Exception as e:
        print(f"[ERROR] 疗法分类API发生异常: {str(e)}")
        print(f"[ERROR] 完整错误堆栈: {traceback.format_exc()}")
        raise HttpError(500, f"获取疗法分类失败: {str(e)}")

@prognosis_router.post("/therapy-classifications", response=PrognosisTherapyClassificationSchema)
@api_timer("创建疗法分类")
@rate_limit("create_therapy_classification_api", normal_limit=5, member_limit=20)  # 创建操作限制严格
async def create_therapy_classification(request, payload: PrognosisTherapyClassificationIn):
    """
    创建疗法分类（管理员）
    
    【测试URL】: /api/routertest1/prognosis/therapy-classifications
    """
    try:
        print(f"[DEBUG] 开始创建疗法分类 - user_id: {getattr(request, 'user_id', 'unknown')}")
        print(f"[DEBUG] 分类数据: {payload.dict()}")
        
        # 检查code是否已存在
        existing = await get_async(PrognosisTherapyClassification, code=payload.code)
        if existing:
            raise HttpError(400, f"分类编码 '{payload.code}' 已存在")
        
        classification = PrognosisTherapyClassification(**payload.dict())
        await save_async(classification)
        
        result = {
            'id': classification.id,
            'name': classification.name,
            'code': classification.code,
            'description': classification.description,
            'icon': classification.icon,
            'color': classification.color,
            'sort_order': classification.sort_order,
            'is_active': classification.is_active,
            'created_at': classification.created_at,
            'updated_at': classification.updated_at,
            'therapy_count': 0
        }
        
        print(f"[DEBUG] 疗法分类创建成功，ID: {classification.id}")
        return result
        
    except Exception as e:
        print(f"[ERROR] 创建疗法分类失败: {str(e)}")
        print(f"[ERROR] 错误堆栈: {traceback.format_exc()}")
        raise HttpError(500, f"创建疗法分类失败: {str(e)}")

@prognosis_router.get("/therapy-classifications/{classification_id}/therapies", response=List[PrognosisTherapyCategoryDetailSchema])
@therapy_list_cache(timeout=CacheTimeout.SHORT)  # 🎯 使用专用缓存装饰器，3分钟缓存
@api_timer("根据分类获取疗法列表")
@rate_limit("therapies_by_classification_api", normal_limit=35, member_limit=140)  # 疗法列表查询
async def list_therapies_by_classification(request, classification_id: int, page: int = 1, size: int = 20):
    """
    获取指定分类下的疗法列表（系统疗法 + 用户疗法）- 高性能缓存版本 + 分页支持
    
    【测试URL】: /api/routertest1/prognosis/therapy-classifications/{id}/therapies?page=1&size=20
    
    参数说明:
    - page: 页码，从1开始 (默认: 1)
    - size: 每页数量 (默认: 20, 最大: 100)
    """
    try:
        print(f"[DEBUG] 获取分类下的疗法列表 - classification_id: {classification_id}, page: {page}, size: {size}")
        
        # 参数验证
        if page < 1:
            page = 1
        if size < 1 or size > 100:  # 限制最大每页数量
            size = 20
        
        # 计算偏移量
        offset = (page - 1) * size
        
        # 先验证分类是否存在
        classification = await get_async(PrognosisTherapyClassification, id=classification_id, is_active=True)
        if not classification:
            raise HttpError(404, "疗法分类不存在")
        
        print(f"[DEBUG] 找到分类: {classification.name} (code: {classification.code})")
        
        # 1. 获取系统疗法 - 支持分页
        system_therapies_query = await sync_to_async(lambda: list(
            PrognosisTherapyCategory.objects.filter(
                classification=classification,
                is_active=True
            ).order_by('-created_at')[offset:offset+size]
        ))()
        print(f"[DEBUG] 查询到系统疗法数量: {len(system_therapies_query)}")
        
        # 2. 获取用户疗法 - 支持分页（通过category字段匹配分类code）
        user_therapies_query = await sync_to_async(lambda: list(
            PrognosisUserTherapy.objects.filter(
                category=classification.code,
                is_public=True  # 只显示公开的用户疗法
            ).order_by('-created_at')[offset:offset+size]
        ))()
        print(f"[DEBUG] 查询到用户疗法数量: {len(user_therapies_query)}")
        
        result = []
        
        # 处理系统疗法
        for therapy in system_therapies_query:
            difficulty_choices = {1: '简单', 2: '中等', 3: '困难'}
            
            # 使用sync_to_async获取关联的分类名称
            classification_name = None
            if therapy.classification_id:
                try:
                    cls = await sync_to_async(lambda: therapy.classification)()
                    classification_name = await sync_to_async(lambda: cls.name)() if cls else None
                except Exception as e:
                    print(f"[WARNING] 获取系统疗法分类名称失败: {e}")
                    classification_name = classification.name
            
            # 🚀 使用异步方法获取统计数据，避免阻塞
            try:
                stats = await therapy.async_all_stats()
                user_count = stats['user_count']
                likes_count = stats['likes_count']
                rating = stats['rating']
                print(f"[DEBUG] 异步获取疗法 {therapy.name} 统计数据: {stats}")
            except Exception as e:
                print(f"[WARNING] 异步获取疗法统计数据失败: {e}")
                user_count, likes_count, rating = 0, 0, 0.0
            
            therapy_dict = {
                'id': therapy.id,
                'name': therapy.name,
                'description': therapy.description,
                'instructions': therapy.instructions,
                'image': therapy.image,
                'color': therapy.color,
                'duration': therapy.duration,
                'difficulty_level': therapy.difficulty_level,
                'difficulty_name': difficulty_choices.get(therapy.difficulty_level, '简单'),
                'contraindications': therapy.contraindications,
                'is_recommended': therapy.is_recommended,
                'is_active': therapy.is_active,
                'created_at': therapy.created_at,
                'updated_at': therapy.updated_at,
                'classification_id': therapy.classification_id,
                'classification_name': classification_name or classification.name,
                'user_count': user_count,  # 🚀 真实的异步统计数据
                'likes_count': likes_count,  # 🚀 真实的异步统计数据
                'rating': rating  # 🚀 真实的异步统计数据
            }
            result.append(therapy_dict)
        
        # 处理用户疗法 - 转换为系统疗法格式（优化：批量处理，减少数据库查询）
        for user_therapy in user_therapies_query:
            
            therapy_dict = {
                'id': user_therapy.id + 100000,  # 通过加大数避免ID冲突，而不是字符串前缀
                'name': f"{user_therapy.name}",  # 简化显示
                'description': user_therapy.description or "用户自创疗法",
                'instructions': user_therapy.instructions or "请参考疗法描述",
                'image': "https://example.com/user-therapy.jpg",  # 默认用户疗法图片
                'color': "#52c41a",  # 用户疗法用绿色
                'duration': user_therapy.duration or "自定义时长",
                'difficulty_level': 1,  # 默认值，该字段不存在
                'difficulty_name': '简单',  # 默认值
                'contraindications': f"适用体质: {user_therapy.constitution_1 or '通用'}",
                'is_recommended': False,  # 用户疗法默认不推荐
                'is_active': True,  # 默认值，该字段不存在
                'created_at': user_therapy.created_at,
                'updated_at': user_therapy.updated_at,
                'classification_id': classification_id,
                'classification_name': classification.name,
                'user_count': user_therapy.usage_count,  # 使用实际存在的字段
                'likes_count': 0,  # 用户疗法暂无点赞
                'rating': 0.0  # 用户疗法暂无评分
            }
            result.append(therapy_dict)
        
        # 按创建时间排序，新的在前
        result.sort(key=lambda x: x['created_at'], reverse=True)
        
        print(f"[DEBUG] 分类 {classification.name} 第{page}页共有 {len(result)} 个疗法 (系统: {len(system_therapies_query)}, 用户: {len(user_therapies_query)})")
        return result
        
    except Exception as e:
        print(f"[ERROR] 获取分类下疗法列表失败: {str(e)}")
        print(f"[ERROR] 错误堆栈: {traceback.format_exc()}")
        raise HttpError(500, f"获取疗法列表失败: {str(e)}")

# ===============================================
# 📝 疗法管理相关的API端点
# ===============================================

@prognosis_router.get("/therapies", response=List[PrognosisTherapyCategorySchema])
@therapy_list_cache(timeout=CacheTimeout.MEDIUM)  # 🎯 使用专用缓存装饰器，5分钟缓存
@api_timer("获取所有疗法列表")
@rate_limit("all_therapies_api", normal_limit=40, member_limit=150)  # 疗法查询相对频繁
async def list_therapies(request, page: int = 1, size: int = 20, category_type: str = None):
    """
    获取所有疗法类别 - 高性能缓存版本，支持分页和分类过滤
    
    【测试URL】: /api/routertest1/prognosis/therapies?page=1&size=20&category_type=observe
    
    参数说明:
    - page: 页码，从1开始 (默认: 1)
    - size: 每页数量 (默认: 20, 最大: 100)
    - category_type: 分类类型过滤 (可选: observe - 观察类分类, therapy - 疗法类分类)
    """
    try:
        print(f"[DEBUG] 开始处理疗法列表请求 - user_id: {getattr(request, 'user_id', 'unknown')}, page: {page}, size: {size}, category_type: {category_type}")
        
        # 参数验证
        if page < 1:
            page = 1
        if size < 1 or size > 100:
            size = 20
            
        # 计算偏移量
        offset = (page - 1) * size
        
        # 缓存未命中，检查数据库并可能初始化默认数据
        therapy_count = await count_async(PrognosisTherapyCategory)
        print(f"[DEBUG] 数据库中疗法数量: {therapy_count}")
        
        if therapy_count == 0:
            print("[DEBUG] 数据库中无疗法数据，开始创建默认数据...")
            # 创建默认疗法数据
            await create_default_therapy_data()
        
        # 🎯 根据category_type构建查询条件
        query_filter = {'is_active': True}
        
        if category_type == 'observe':
            # 观察类分类：以 _observe 结尾的分类
            print(f"[DEBUG] 过滤观察类分类（以_observe结尾）")
            therapies_query = await sync_to_async(lambda: list(
                PrognosisTherapyCategory.objects.filter(
                    is_active=True,
                    classification__code__endswith='_observe'
                ).select_related('classification')
                .order_by('-created_at')[offset:offset+size]
            ))()
        elif category_type == 'therapy':
            # 疗法类分类：不以 _observe 结尾的分类
            print(f"[DEBUG] 过滤疗法类分类（不以_observe结尾）")
            therapies_query = await sync_to_async(lambda: list(
                PrognosisTherapyCategory.objects.filter(
                    is_active=True,
                    classification__isnull=False
                ).exclude(
                    classification__code__endswith='_observe'
                ).select_related('classification')
                .order_by('-created_at')[offset:offset+size]
            ))()
        else:
            # 默认：所有疗法
            print(f"[DEBUG] 获取所有疗法（无分类过滤）")
            therapies_query = await sync_to_async(lambda: list(
                PrognosisTherapyCategory.objects.filter(
                    is_active=True
                ).order_by('-created_at')[offset:offset+size]
            ))()
        
        print(f"[DEBUG] 最终查询到疗法数量: {len(therapies_query)}")
        
        result = []
        for therapy in therapies_query:
            # 使用sync_to_async获取关联的分类名称和编码
            classification_name = None
            classification_code = None
            if therapy.classification_id:
                try:
                    classification = await sync_to_async(lambda: therapy.classification)()
                    if classification:
                        classification_name = await sync_to_async(lambda: classification.name)()
                        classification_code = await sync_to_async(lambda: classification.code)()
                        print(f"[DEBUG] 疗法 {therapy.name} 属于分类: {classification_name} (code: {classification_code})")
                except Exception as e:
                    print(f"[WARNING] 获取分类名称失败: {e}")
                    classification_name = None
                    classification_code = None
            
            therapy_dict = {
                'id': therapy.id,
                'name': therapy.name,
                'description': therapy.description,
                'image': therapy.image,
                'color': therapy.color,
                'duration': therapy.duration,
                'is_recommended': therapy.is_recommended,
                'is_active': therapy.is_active,
                'created_at': therapy.created_at,
                'updated_at': therapy.updated_at,
                'classification_id': therapy.classification_id,
                'classification_name': classification_name,
            }
            result.append(therapy_dict)
        
        # 🎯 打印过滤结果统计
        if category_type == 'observe':
            observe_count = len([r for r in result if r['classification_name'] and '_observe' in str(r['classification_name']).lower()])
            print(f"[DEBUG] 观察类疗法过滤结果: {observe_count}/{len(result)} 个疗法")
        elif category_type == 'therapy':
            therapy_count = len([r for r in result if r['classification_name'] and '_observe' not in str(r['classification_name']).lower()])
            print(f"[DEBUG] 疗法类疗法过滤结果: {therapy_count}/{len(result)} 个疗法")
        
        print(f"[DEBUG] 疗法列表API处理完成，第{page}页返回数据数量: {len(result)}")
        print(f"[DEBUG] 返回的疗法分类统计:")
        classification_stats = {}
        for therapy in result:
            cls_name = therapy['classification_name'] or '未分类'
            classification_stats[cls_name] = classification_stats.get(cls_name, 0) + 1
        for cls_name, count in classification_stats.items():
            print(f"[DEBUG]   - {cls_name}: {count} 个")
        
        return result
        
    except Exception as e:
        print(f"[ERROR] 疗法列表API发生异常: {str(e)}")
        print(f"[ERROR] 完整错误堆栈: {traceback.format_exc()}")
        raise HttpError(500, f"获取疗法列表失败: {str(e)}")

async def create_default_therapy_data():
    """创建默认疗法数据"""
    try:
        
        print("[DEBUG] 开始创建默认疗法数据...")
        
        # 创建默认分类
        default_classifications = [
            {"name": "功法疗法", "code": "gongfa_therapy", "description": "传统功法和冥想疗法", "color": "#4CAF50", "sort_order": 1},
            {"name": "音乐疗法", "code": "music_therapy", "description": "音乐治疗和声波疗法", "color": "#2196F3", "sort_order": 2},
            {"name": "按摩疗法", "code": "massage_therapy", "description": "推拿按摩和理疗", "color": "#FF9800", "sort_order": 3},
        ]
        
        classifications = {}
        for cls_data in default_classifications:
            print(f"[DEBUG] 创建分类: {cls_data['name']}")
            existing = await get_async(PrognosisTherapyClassification, code=cls_data["code"])
            if not existing:
                cls_obj = PrognosisTherapyClassification(**cls_data)
                await save_async(cls_obj)
                classifications[cls_data["code"]] = cls_obj
                print(f"[DEBUG] 分类 {cls_data['name']} 创建成功")
            else:
                classifications[cls_data["code"]] = existing
                print(f"[DEBUG] 分类 {cls_data['name']} 已存在")
        
        # 创建默认疗法
        default_therapies = [
            {
                "name": "功法冥想",
                "description": "通过传统功法和冥想来缓解压力和焦虑，提高注意力和情绪管理能力。",
                "image": "https://example.com/gongfa.jpg",  
                "color": "#4CAF50",
                "duration": "15-30分钟",
                "instructions": "1. 找一个安静的地方坐下 2. 调整呼吸节奏 3. 专注内心感受",
                "difficulty_level": 1,
                "contraindications": "",
                "is_recommended": True,
                "is_active": True,
                "classification": classifications["gongfa_therapy"]
            },
            {
                "name": "五音疗法",
                "description": "基于中医五音理论，利用音乐的节奏和旋律来调节情绪，改善心理健康。",
                "image": "https://example.com/music.jpg",
                "color": "#2196F3",
                "duration": "20-40分钟",
                "instructions": "1. 选择适合的五音音乐 2. 调整音量到舒适水平 3. 放松身心聆听",
                "difficulty_level": 1,
                "contraindications": "",
                "is_recommended": True,
                "is_active": True,
                "classification": classifications["music_therapy"]
            },
            {
                "name": "推拿养生",
                "description": "通过专业的推拿手法来疏通经络，缓解身体疲劳，促进气血循环。",
                "image": "https://example.com/massage.jpg",
                "color": "#FF9800",
                "duration": "30-60分钟",
                "instructions": "1. 准备推拿用具 2. 按照穴位图进行按摩 3. 注意力度适中",
                "difficulty_level": 2,
                "contraindications": "急性外伤、皮肤病患者慎用",
                "is_recommended": True,
                "is_active": True,
                "classification": classifications["massage_therapy"]
            },
        ]
        
        for therapy_data in default_therapies:
            print(f"[DEBUG] 创建疗法: {therapy_data['name']}")
            existing = await get_async(PrognosisTherapyCategory, name=therapy_data['name'])
            if not existing:
                therapy = PrognosisTherapyCategory(**therapy_data)
                await save_async(therapy)
                print(f"[DEBUG] 疗法 {therapy_data['name']} 创建成功，ID: {therapy.id}")
            else:
                print(f"[DEBUG] 疗法 {therapy_data['name']} 已存在")
                
        print("[DEBUG] 所有默认数据创建完成")
        
    except Exception as e:
        print(f"[ERROR] 创建默认疗法数据失败: {str(e)}")
        print(f"[ERROR] 错误堆栈: {traceback.format_exc()}")

@prognosis_router.get("/therapies/{therapy_id}/", response=PrognosisTherapyCategoryDetailSchema)
@therapy_detail_cache(timeout=CacheTimeout.LONG)  # 🎯 使用专用缓存装饰器，30分钟缓存
@api_timer("获取疗法详情")
@rate_limit("therapy_detail_api", normal_limit=50, member_limit=200)  # 详情查询频繁
async def get_therapy(request, therapy_id: int):
    """
    获取特定疗法的详情（支持系统疗法和用户疗法）
    
    【测试URL】: /api/routertest1/prognosis/therapies/{id}
    说明：ID < 100000 为系统疗法，ID >= 100000 为用户疗法
    """
    try:
        print(f"[DEBUG] 获取疗法详情 - therapy_id: {therapy_id}")
        
        # 判断是系统疗法还是用户疗法
        if therapy_id >= 100000:
            # 用户疗法：实际ID = therapy_id - 100000
            actual_user_therapy_id = therapy_id - 100000
            print(f"[DEBUG] 查询用户疗法 - 实际ID: {actual_user_therapy_id}")
            
            user_therapy = await get_async(PrognosisUserTherapy, id=actual_user_therapy_id)
            if not user_therapy:
                raise HttpError(404, "用户疗法不存在")
            
            # 获取分类信息
            classification = None
            classification_name = None
            classification_id = None
            if user_therapy.category:
                try:
                    classification = await get_async(PrognosisTherapyClassification, code=user_therapy.category)
                    if classification:
                        classification_name = classification.name
                        classification_id = classification.id
                except Exception as e:
                    print(f"[WARNING] 获取用户疗法分类失败: {e}")
            
            result = {
                'id': therapy_id,  # 返回传入的ID，保持一致性
                'name': f"{user_therapy.name}",
                'description': user_therapy.description or "用户自创疗法",
                'instructions': user_therapy.instructions or "请参考疗法描述",
                'image': "https://example.com/user-therapy.jpg",
                'color': "#52c41a",
                'duration': user_therapy.duration or "自定义时长",
                'difficulty_level': 1,  # 默认值
                'difficulty_name': '简单',  # 默认值
                'contraindications': f"适用体质: {user_therapy.constitution_1 or '通用'}",
                'is_recommended': False,
                'is_active': True,  # 默认值
                'created_at': user_therapy.created_at,
                'updated_at': user_therapy.updated_at,
                'classification_id': classification_id,
                'classification_name': classification_name,
                'user_count': user_therapy.usage_count,
                'likes_count': 0,
                'rating': 0.0
            }
            
            print(f"[DEBUG] 用户疗法详情获取成功: {user_therapy.name}")
            return result
            
        else:
            # 系统疗法
            print(f"[DEBUG] 查询系统疗法 - ID: {therapy_id}")
            therapy = await get_async(PrognosisTherapyCategory, id=therapy_id, is_active=True)
            if not therapy:
                raise HttpError(404, "系统疗法不存在")
            
            # 获取难度等级显示文本
            difficulty_choices = {1: '简单', 2: '中等', 3: '困难'}
            
            # 使用sync_to_async获取关联的分类名称
            classification_name = None
            if therapy.classification_id:
                try:
                    classification = await sync_to_async(lambda: therapy.classification)()
                    classification_name = await sync_to_async(lambda: classification.name)() if classification else None
                except Exception as e:
                    print(f"[WARNING] 获取分类名称失败: {e}")
                    classification_name = None
            
            # 🚀 使用异步方法获取统计数据，获取真实数据而不是默认值
            try:
                stats = await therapy.async_all_stats()
                user_count = stats['user_count']
                likes_count = stats['likes_count']
                rating = stats['rating']
                print(f"[DEBUG] 异步获取疗法详情 {therapy.name} 统计数据: {stats}")
            except Exception as e:
                print(f"[WARNING] 异步获取疗法详情统计数据失败: {e}")
                user_count, likes_count, rating = 0, 0, 0.0
            
            result = {
                'id': therapy.id,
                'name': therapy.name,
                'description': therapy.description,
                'instructions': therapy.instructions,
                'image': therapy.image,
                'color': therapy.color,
                'duration': therapy.duration,
                'difficulty_level': therapy.difficulty_level,
                'difficulty_name': difficulty_choices.get(therapy.difficulty_level, '简单'),
                'contraindications': therapy.contraindications,
                'is_recommended': therapy.is_recommended,
                'is_active': therapy.is_active,
                'created_at': therapy.created_at,
                'updated_at': therapy.updated_at,
                'classification_id': therapy.classification_id,
                'classification_name': classification_name,
                'user_count': user_count,  # 🚀 真实的异步统计数据
                'likes_count': likes_count,  # 🚀 真实的异步统计数据
                'rating': rating  # 🚀 真实的异步统计数据
            }
            
            print(f"[DEBUG] 系统疗法详情获取成功: {therapy.name}")
            return result
        
    except Exception as e:
        print(f"[ERROR] 获取疗法详情失败: {str(e)}")
        import traceback
        print(f"[ERROR] 错误堆栈: {traceback.format_exc()}")
        raise HttpError(500, f"获取疗法详情失败: {str(e)}")

@prognosis_router.post("/therapies", response=PrognosisTherapyCategoryDetailSchema)
@api_timer("创建疗法")
@rate_limit("create_therapy_api", normal_limit=5, member_limit=20)  # 创建操作限制严格
async def create_therapy(request, payload: PrognosisTherapyCategoryIn):
    """
    创建疗法
    
    【测试URL】: /api/routertest1/prognosis/therapies
    """
    try:
        from api.models import PrognosisTherapyCategory, PrognosisTherapyClassification
        from api.ninja_apis.async_utils import save_async, get_async
        
        print(f"[DEBUG] 开始创建疗法 - user_id: {getattr(request, 'user_id', 'unknown')}")
        print(f"[DEBUG] 疗法数据: {payload.dict()}")
        
        # 验证分类是否存在
        if payload.classification_id:
            classification = await get_async(PrognosisTherapyClassification, id=payload.classification_id, is_active=True)
            if not classification:
                from ninja.errors import HttpError
                raise HttpError(400, "指定的疗法分类不存在")
        
        therapy = PrognosisTherapyCategory(**payload.dict())
        await save_async(therapy)
        
        # 获取难度等级显示文本
        difficulty_choices = {1: '简单', 2: '中等', 3: '困难'}
        
        # 使用sync_to_async获取关联的分类名称
        classification_name = None
        if therapy.classification_id:
            try:
                from asgiref.sync import sync_to_async
                classification = await sync_to_async(lambda: therapy.classification)()
                classification_name = await sync_to_async(lambda: classification.name)() if classification else None
            except Exception as e:
                print(f"[WARNING] 获取分类名称失败: {e}")
                classification_name = None
        
        result = {
            'id': therapy.id,
            'name': therapy.name,
            'description': therapy.description,
            'instructions': therapy.instructions,
            'image': therapy.image,
            'color': therapy.color,
            'duration': therapy.duration,
            'difficulty_level': therapy.difficulty_level,
            'difficulty_name': difficulty_choices.get(therapy.difficulty_level, '简单'),
            'contraindications': therapy.contraindications,
            'is_recommended': therapy.is_recommended,
            'is_active': therapy.is_active,
            'created_at': therapy.created_at,
            'updated_at': therapy.updated_at,
            'classification_id': therapy.classification_id,
            'classification_name': classification_name,
            'user_count': 0,
            'likes_count': 0,
            'rating': 0.0
        }
        
        print(f"[DEBUG] 疗法创建成功，ID: {therapy.id}")
        return result
        
    except Exception as e:
        print(f"[ERROR] 创建疗法失败: {str(e)}")
        import traceback
        print(f"[ERROR] 错误堆栈: {traceback.format_exc()}")
        from ninja.errors import HttpError
        raise HttpError(500, f"创建疗法失败: {str(e)}")

# ===============================================
# 📝 用户疗法管理相关的API端点
# ===============================================

@prognosis_router.post("/user-therapies", response=PrognosisUserTherapySchema)
@api_timer("创建用户疗法")
@rate_limit("create_user_therapy_api", normal_limit=10, member_limit=40)  # 用户疗法创建适中限制
async def create_user_therapy(request, payload: PrognosisUserTherapyIn):
    """
    用户创建自定义疗法
    
    【测试URL】: /api/routertest1/prognosis/user-therapies
    """
    try:
        from api.models import PrognosisUserTherapy
        from api.ninja_apis.async_utils import save_async
        
        user_id = request.user_id
        print(f"[DEBUG] 用户创建疗法 - user_id: {user_id}")
        print(f"[DEBUG] 疗法数据: {payload.dict()}")
        
        therapy_data = payload.dict()
        therapy_data['creator_id'] = user_id
        therapy = PrognosisUserTherapy(**therapy_data)
        await save_async(therapy)
        
        result = {
            'id': therapy.id,
            'user_id': therapy.creator_id,
            'name': therapy.name,
            'description': therapy.description,
            'instructions': therapy.instructions,
            'category': therapy.category,
            'duration': therapy.duration,
            'difficulty_level': 1,  # 默认值，该字段不存在
            'tags': '',  # 默认值，该字段不存在
            'is_public': therapy.is_public,
            'is_active': True,  # 默认值，该字段不存在
            'created_at': therapy.created_at,
            'updated_at': therapy.updated_at
        }
        
        print(f"[DEBUG] 用户疗法创建成功，ID: {therapy.id}")
        return result
        
    except Exception as e:
        print(f"[ERROR] 创建用户疗法失败: {str(e)}")
        import traceback
        print(f"[ERROR] 错误堆栈: {traceback.format_exc()}")
        from ninja.errors import HttpError
        raise HttpError(500, f"创建用户疗法失败: {str(e)}")

@prognosis_router.get("/user-therapies", response=List[PrognosisUserTherapySchema])
@user_therapy_cache(timeout=CacheTimeout.SHORT)  # 🎯 使用专用缓存装饰器，5分钟缓存
@api_timer("获取用户疗法列表")
@rate_limit("user_therapies_api", normal_limit=30, member_limit=120)  # 用户疗法查询适中限制
async def list_user_therapies(request, page: int = 1, size: int = 20):
    """
    获取用户创建的疗法列表 - 支持分页
    
    【测试URL】: /api/routertest1/prognosis/user-therapies?page=1&size=20
    
    参数说明:
    - page: 页码，从1开始 (默认: 1)
    - size: 每页数量 (默认: 20, 最大: 100)
    """
    try:
        from api.models import PrognosisUserTherapy
        from api.ninja_apis.async_utils import filter_async
        
        user_id = request.user_id
        print(f"[DEBUG] 获取用户疗法列表 - user_id: {user_id}, page: {page}, size: {size}")
        
        # 参数验证
        if page < 1:
            page = 1
        if size < 1 or size > 100:
            size = 20
            
        # 计算偏移量
        offset = (page - 1) * size

        # 使用Django ORM的分页查询
        user_therapies_query = await sync_to_async(lambda: list(
            PrognosisUserTherapy.objects.filter(
                creator_id=user_id
            ).order_by('-created_at')[offset:offset+size]
        ))()
        
        result = []
        for therapy in user_therapies_query:
            result.append({
                'id': therapy.id,
                'user_id': therapy.creator_id,
                'name': therapy.name,
                'description': therapy.description,
                'instructions': therapy.instructions,
                'category': therapy.category,
                'duration': therapy.duration,
                'difficulty_level': 1,  # 默认值，该字段不存在
                'tags': '',  # 默认值，该字段不存在
                'is_public': therapy.is_public,
                'is_active': True,  # 默认值，该字段不存在
                'created_at': therapy.created_at,
                'updated_at': therapy.updated_at
            })
        
        print(f"[DEBUG] 用户疗法查询完成，第{page}页返回数量: {len(result)}")
        return result
        
    except Exception as e:
        print(f"[ERROR] 获取用户疗法失败: {str(e)}")
        import traceback
        print(f"[ERROR] 错误堆栈: {traceback.format_exc()}")
        from ninja.errors import HttpError
        raise HttpError(500, f"获取用户疗法失败: {str(e)}")

# ===============================================
# 🔍 模块导出和状态检查
# ===============================================

def get_router_info():
    """获取路由器信息 - 用于调试"""
    return {
        "module": "prognosis_therapy",
        "router_type": "ninja.Router",
        "endpoints_count": len(getattr(prognosis_router, 'operations', [])),
        "status": "ready_for_registration",
        "endpoints": [
            "/therapy-classifications - 疗法分类管理",
            "/therapies - 疗法管理",
            "/user-therapies - 用户疗法管理"
        ]
    }

def get_test_endpoints():
    """获取测试端点列表"""
    return [
        "GET /api/routertest1/prognosis/therapy-classifications - 获取所有疗法分类",
        "POST /api/routertest1/prognosis/therapy-classifications - 创建疗法分类",
        "GET /api/routertest1/prognosis/therapy-classifications/{id}/therapies - 获取分类下的疗法",
        "GET /api/routertest1/prognosis/therapies - 获取所有疗法",
        "GET /api/routertest1/prognosis/therapies/{id} - 获取疗法详情",
        "POST /api/routertest1/prognosis/therapies - 创建疗法",
        "POST /api/routertest1/prognosis/user-therapies - 创建用户疗法",
        "GET /api/routertest1/prognosis/user-therapies - 获取用户疗法列表"
    ]

# 🎯 重要提示：
# 1. 本模块只定义therapy相关路由，不执行注册
# 2. 移除了症状管理和体质管理API，只保留疗法相关功能
# 3. 路由注册由 api/ninja_apis/__init__.py 统一管理
# 4. 使用Router而非NinjaAPI避免冲突
# 5. 所有端点都是异步的，符合项目规范
# 6. 严格遵循 routertest1 的格式和注释规范

# 🔥 导入交互功能模块，自动注册评论、点赞、使用记录等API到当前路由器
try:
    from . import prognosis_interaction_api
    print("[DEBUG] ✅ 疗法交互功能模块已导入，API端点已自动添加到prognosis_router")
except Exception as e:
    print(f"[WARNING] ❌ 疗法交互功能模块导入失败: {e}")

# 🧬 导入体质相似度功能模块，自动注册体质相似度排序API到当前路由器
try:
    from . import prognosis_constitution_similarity_api
    print("[DEBUG] ✅ 体质相似度功能模块已导入，API端点已自动添加到prognosis_router")
except Exception as e:
    print(f"[WARNING] ❌ 体质相似度功能模块导入失败: {e}") 