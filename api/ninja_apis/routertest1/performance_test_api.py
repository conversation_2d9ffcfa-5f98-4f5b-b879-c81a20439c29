# api/ninja_apis/routertest1/performance_test_api.py
# 并发性能测试API - 无限流机制，使用缓存优化
import time
import json
import asyncio
import functools
import random
import hashlib
from typing import List, Optional, Dict, Any
from ninja import Router
from pydantic import BaseModel
from django.http import HttpRequest
from django.core.cache import cache
from api.utils.async_utils import get_async, filter_async, save_async
from api.models import UserInfo, Activity, UserGoal
import logging

logger = logging.getLogger(__name__)

def api_timer(func_name=None):
    """
    API耗时计算装饰器
    打印API执行时间，用于性能监控和优化
    
    Args:
        func_name: 自定义函数名，如果不提供则使用实际函数名
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            name = func_name or func.__name__
            print(f"[API_TIMER] 🚀 {name} 开始执行")
            
            try:
                result = await func(*args, **kwargs)
                end_time = time.time()
                duration = end_time - start_time
                print(f"[API_TIMER] ✅ {name} 执行完成，耗时: {duration:.3f}秒")
                return result
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                print(f"[API_TIMER] ❌ {name} 执行失败，耗时: {duration:.3f}秒，错误: {str(e)}")
                raise
                
        return wrapper
    return decorator

def cache_decorator(timeout=300, key_prefix="perf_test"):
    """
    缓存装饰器 - 用于GET请求缓存优化
    
    Args:
        timeout: 缓存超时时间（秒）
        key_prefix: 缓存键前缀
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(request, *args, **kwargs):
            # 生成缓存键
            cache_key_parts = [
                key_prefix,
                func.__name__,
                str(getattr(request, 'user_id', 'anonymous')),
                hashlib.md5(str(args + tuple(kwargs.items())).encode()).hexdigest()[:8]
            ]
            cache_key = ":".join(cache_key_parts)
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                print(f"[CACHE_HIT] ⚡ {cache_key}")
                return cached_result
            
            # 缓存未命中，执行函数
            print(f"[CACHE_MISS] 🔄 {cache_key}")
            result = await func(request, *args, **kwargs)
            
            # 存储到缓存
            cache.set(cache_key, result, timeout)
            print(f"[CACHE_SET] 💾 {cache_key} (TTL: {timeout}s)")
            
            return result
        return wrapper
    return decorator

# 响应模型
class PerformanceTestResponse(BaseModel):
    """性能测试响应模型"""
    success: bool
    message: str
    data: Dict[str, Any]
    execution_time: float
    cache_hit: bool = False
    timestamp: str

class UserDataResponse(BaseModel):
    """用户数据响应模型"""
    user_id: int
    username: str
    activities_count: int
    goals_count: int
    cache_hit: bool = False

class ComputeTaskResponse(BaseModel):
    """计算任务响应模型"""
    task_id: str
    result: int
    iterations: int
    execution_time: float
    cache_hit: bool = False

# 性能测试API路由 - 注册到routertest1_router
from .test_api import routertest1_router

@routertest1_router.get("/perf/simple", response=PerformanceTestResponse, tags=["性能测试"])
@api_timer("简单性能测试")
@cache_decorator(timeout=60, key_prefix="simple_test")
async def simple_performance_test(request: HttpRequest) -> PerformanceTestResponse:
    """
    简单性能测试API - 无限流机制
    用于测试基础API响应性能
    """
    start_time = time.time()
    
    # 模拟一些简单计算
    result = sum(range(1000))
    
    # 模拟异步操作
    await asyncio.sleep(0.01)  # 10ms延迟
    
    execution_time = time.time() - start_time
    
    return PerformanceTestResponse(
        success=True,
        message="简单性能测试完成",
        data={
            "calculation_result": result,
            "random_number": random.randint(1, 1000),
            "user_id": getattr(request, 'user_id', None)
        },
        execution_time=execution_time,
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
    )

@routertest1_router.get("/perf/database", response=UserDataResponse, tags=["性能测试"])
@api_timer("数据库性能测试")
@cache_decorator(timeout=120, key_prefix="db_test")
async def database_performance_test(request: HttpRequest) -> UserDataResponse:
    """
    数据库性能测试API - 测试数据库查询性能
    """
    user_id = getattr(request, 'user_id', 2)  # 默认用户ID为2
    
    try:
        # 异步查询用户信息
        user = await get_async(UserInfo, id=user_id)
        
        # 异步查询用户活动数量
        activities = await filter_async(Activity, user_id=user_id)
        activities_count = len(activities) if activities else 0
        
        # 异步查询用户目标数量
        goals = await filter_async(UserGoal, user_id=user_id)
        goals_count = len(goals) if goals else 0
        
        return UserDataResponse(
            user_id=user.id,
            username=user.username if hasattr(user, 'username') else f"user_{user.id}",
            activities_count=activities_count,
            goals_count=goals_count
        )
        
    except Exception as e:
        logger.error(f"数据库查询失败: {e}")
        # 返回默认数据
        return UserDataResponse(
            user_id=user_id,
            username=f"user_{user_id}",
            activities_count=0,
            goals_count=0
        )

@routertest1_router.get("/perf/compute/{iterations}", response=ComputeTaskResponse, tags=["性能测试"])
@api_timer("计算密集型性能测试")
@cache_decorator(timeout=300, key_prefix="compute_test")
async def compute_performance_test(request: HttpRequest, iterations: int) -> ComputeTaskResponse:
    """
    计算密集型性能测试API
    
    Args:
        iterations: 计算迭代次数（1-10000）
    """
    start_time = time.time()
    
    # 限制迭代次数防止服务器过载
    iterations = min(max(iterations, 1), 10000)
    
    # 生成任务ID
    task_id = hashlib.md5(f"{time.time()}_{iterations}".encode()).hexdigest()[:12]
    
    # 执行计算密集型任务
    result = 0
    for i in range(iterations):
        result += i * i
        if i % 100 == 0:
            await asyncio.sleep(0)  # 让出控制权
    
    execution_time = time.time() - start_time
    
    return ComputeTaskResponse(
        task_id=task_id,
        result=result,
        iterations=iterations,
        execution_time=execution_time
    )

@routertest1_router.get("/perf/mixed/{complexity}", response=PerformanceTestResponse, tags=["性能测试"])
@api_timer("混合性能测试")
@cache_decorator(timeout=180, key_prefix="mixed_test")
async def mixed_performance_test(request: HttpRequest, complexity: int) -> PerformanceTestResponse:
    """
    混合性能测试API - 结合数据库查询和计算任务
    
    Args:
        complexity: 复杂度级别（1-5）
    """
    start_time = time.time()
    complexity = min(max(complexity, 1), 5)
    user_id = getattr(request, 'user_id', 2)
    
    # 并发执行多个任务
    tasks = []
    
    # 任务1：数据库查询
    async def db_task():
        try:
            user = await get_async(UserInfo, id=user_id)
            return {"user_found": True, "user_id": user.id}
        except:
            return {"user_found": False, "user_id": user_id}
    
    # 任务2：计算任务
    async def compute_task():
        result = sum(i * complexity for i in range(100 * complexity))
        return {"compute_result": result}
    
    # 任务3：模拟网络延迟
    async def network_task():
        await asyncio.sleep(0.01 * complexity)
        return {"network_delay": 0.01 * complexity}
    
    # 并发执行所有任务
    db_result, compute_result, network_result = await asyncio.gather(
        db_task(),
        compute_task(),
        network_task()
    )
    
    execution_time = time.time() - start_time
    
    return PerformanceTestResponse(
        success=True,
        message=f"混合性能测试完成 (复杂度: {complexity})",
        data={
            "complexity": complexity,
            "database": db_result,
            "computation": compute_result,
            "network": network_result,
            "user_id": user_id
        },
        execution_time=execution_time,
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
    )

@routertest1_router.get("/perf/status", tags=["性能测试"])
@api_timer("性能测试状态")
async def performance_test_status(request: HttpRequest):
    """
    性能测试状态API - 返回当前系统状态
    """
    return {
        "status": "running",
        "message": "性能测试API正常运行",
        "endpoints": [
            "/api/routertest1_api/perf/simple - 简单性能测试",
            "/api/routertest1_api/perf/database - 数据库性能测试", 
            "/api/routertest1_api/perf/compute/{iterations} - 计算性能测试",
            "/api/routertest1_api/perf/mixed/{complexity} - 混合性能测试"
        ],
        "cache_enabled": True,
        "rate_limit": "无限流机制",
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
    }
