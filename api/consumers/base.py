# api/consumers/base.py
"""
WebSocket消费者基础类和共享工具
"""

from channels.generic.websocket import AsyncWebsocketConsumer
import json
import asyncio
import traceback
import logging
import time
import urllib.parse
from volcenginesdkarkruntime import Ark
from api.utils.rate_limiter import RateLimiter

logger = logging.getLogger(__name__)


class BaseWebSocketConsumer(AsyncWebsocketConsumer):
    """
    WebSocket消费者基础类
    提供通用的连接管理和用户认证功能
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_id = None
        self.start_time = None
        
    async def get_user_id_from_scope(self):
        """
        从scope中获取用户ID，支持多种认证方式
        """
        user_id = None
        debug_info = []
        
        # 方式1：从认证用户对象获取
        user_obj = self.scope.get('user')
        debug_info.append(f"user_obj_type: {type(user_obj)}")
        debug_info.append(f"user_obj_hasattr_id: {hasattr(user_obj, 'id')}")
        
        if hasattr(user_obj, 'id'):
            user_id = user_obj.id
            debug_info.append(f"user_id_from_user_obj: {user_id}")
        
        # 方式2：从查询参数获取user_id (适用于前端传递token的情况)
        if not user_id:
            query_string = self.scope.get('query_string', b'').decode()
            debug_info.append(f"query_string: {query_string}")
            
            if 'user_id=' in query_string:
                parsed = urllib.parse.parse_qs(query_string)
                debug_info.append(f"parsed_query: {parsed}")
                if 'user_id' in parsed:
                    try:
                        user_id = int(parsed['user_id'][0])
                        debug_info.append(f"user_id_from_query: {user_id}")
                    except (ValueError, IndexError) as e:
                        debug_info.append(f"query_parse_error: {e}")
        
        # 方式3：临时允许无认证连接（用于测试）
        if not user_id:
            debug_info.append("fallback_to_default_user")
            logger.warning(f"用户ID未获取到，允许匿名连接（仅供测试）。调试信息: {'; '.join(debug_info)}")
            user_id = 1  # 默认用户ID，生产环境应该移除此行
        
        print(f"[AUTH_DEBUG] 用户认证过程: {'; '.join(debug_info)}")
        return user_id
    
    async def send_error(self, message: str, done: bool = True):
        """
        发送错误消息的通用方法
        """
        try:
            await self.send(text_data=json.dumps({
                'error': message,
                'done': done
            }))
        except Exception as e:
            logger.error(f"发送错误消息失败: {str(e)}")
    
    def log_performance(self, operation: str, start_time: float, success: bool = True):
        """
        记录性能日志的通用方法
        """
        if start_time is None:
            duration = 0.0
        else:
            duration = time.time() - start_time
        status = "✅" if success else "❌"
        log_method = logger.info if success else logger.error
        
        log_method(f"{status} {operation}，用户ID: {self.user_id}，耗时: {duration:.3f}秒")


class ArkBasedConsumer(BaseWebSocketConsumer):
    """
    基于Ark模型的消费者基础类
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.client = None
        self.conversation_history = []
        self.rate_limiter = None
        
    async def initialize_ark_client(self):
        """
        初始化Ark客户端
        """
        from django.conf import settings
        api_key = getattr(settings, 'ARK_API_KEY', None)
        
        if not api_key:
            raise ValueError("ARK_API_KEY未在settings中配置")
            
        self.client = Ark(
            api_key=api_key,
            base_url="https://ark.cn-beijing.volces.com/api/v3"
        )
        
    async def check_rate_limit(self):
        """
        检查限流，返回是否允许继续处理
        """
        if not self.rate_limiter or not self.user_id:
            return True

        limit_info = await self.rate_limiter.check_rate_limit(self.user_id)

        # 打印详细的限流检查信息
        member_status = "会员" if limit_info['is_member'] else "普通用户"
        period = "每日" if limit_info['is_member'] else "每周"
        print(f"[RATE_LIMIT] 用户{self.user_id}({member_status}) 限流检查:")
        print(f"[RATE_LIMIT]   当前调用次数: {limit_info['current_count']}/{limit_info['limit']} ({period})")
        print(f"[RATE_LIMIT]   重置时间: {limit_info['reset_time']}")
        print(f"[RATE_LIMIT]   是否允许: {limit_info['allowed']}")

        if not limit_info['allowed']:
            error_message = f'您{period}的聊天次数已达上限 ({limit_info["current_count"]}/{limit_info["limit"]})'
            if not limit_info['is_member']:
                error_message += '，升级会员可享受更多调用次数'
            error_message += f'，重置时间: {limit_info["reset_time"]}'

            print(f"[RATE_LIMIT] ❌ 限流拦截: {error_message}")
            await self.send_error(error_message)
            return False

        # 增加调用次数
        await self.rate_limiter.increment_count(self.user_id)
        print(f"[RATE_LIMIT] ✅ 通过限流检查，调用次数+1")
        return True
    
    async def send_stream_response(self, response, model_name: str = "ep-20250424123213-kk2fv"):
        """
        处理流式响应的通用方法
        """
        assistant_message = ""
        
        # 实时处理和发送每个数据块
        for chunk in response:
            if chunk.choices and chunk.choices[0].delta and hasattr(chunk.choices[0].delta, 'content'):
                content = chunk.choices[0].delta.content
                if content:
                    assistant_message += content
                    # 立即发送每个数据块到客户端
                    await self.send(text_data=json.dumps({
                        'content': content,
                        'done': False
                    }))
        
        # 发送完成标记
        await self.send(text_data=json.dumps({
            'content': '',
            'done': True
        }))
        
        # 将完整回复添加到历史记录
        self.conversation_history.append({"role": "assistant", "content": assistant_message})
        
        return assistant_message 