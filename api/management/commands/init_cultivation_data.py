# -*- coding: utf-8 -*-
"""
初始化修炼系统基础数据
"""

from django.core.management.base import BaseCommand
from django.db import transaction

from ...models.cultivation_models import CultivationRealm, CultivationAchievement


class Command(BaseCommand):
    help = '初始化修炼系统基础数据（境界、成就等）'

    def handle(self, *args, **options):
        self.stdout.write("开始初始化修炼系统基础数据...")
        
        with transaction.atomic():
            # 初始化修炼境界
            self._init_cultivation_realms()
            
            # 初始化修炼成就
            self._init_cultivation_achievements()
        
        self.stdout.write(
            self.style.SUCCESS('修炼系统基础数据初始化完成！')
        )

    def _init_cultivation_realms(self):
        """初始化修炼境界"""
        self.stdout.write("初始化修炼境界...")
        
        realms = [
            # 炼体期
            {
                'level': 1, 'name': '炼体一重', 'title': '修炼入门',
                'description': '初入修炼门径，开始炼体强身',
                'required_exp': 0, 
                'benefits': {'exp_bonus': 0},
                'cultivation_flavor': '初窥修炼门径，体质略有提升'
            },
            {
                'level': 2, 'name': '炼体二重', 'title': '入门弟子',
                'description': '身体素质明显改善，精力更加充沛',
                'required_exp': 100,
                'benefits': {'exp_bonus': 5},
                'cultivation_flavor': '筋骨初成，力量渐增'
            },
            {
                'level': 3, 'name': '炼体三重', 'title': '勤勉修士',
                'description': '体魄强健，持久力大增',
                'required_exp': 300,
                'benefits': {'exp_bonus': 10},
                'cultivation_flavor': '气血充盈，精神饱满'
            },
            
            # 炼精化气期
            {
                'level': 4, 'name': '炼精化气初期', 'title': '气海初开',
                'description': '体内精气开始转化，进入炼精化气境界',
                'required_exp': 600,
                'benefits': {'exp_bonus': 15, 'health_bonus': 5},
                'cultivation_flavor': '精化为气，内息初生'
            },
            {
                'level': 5, 'name': '炼精化气中期', 'title': '内息绵长',
                'description': '内气运转自如，身体轻盈',
                'required_exp': 1000,
                'benefits': {'exp_bonus': 20, 'health_bonus': 10},
                'cultivation_flavor': '真气流转，周身畅通'
            },
            {
                'level': 6, 'name': '炼精化气后期', 'title': '气息纯净',
                'description': '体内真气纯净，准备突破至更高境界',
                'required_exp': 1500,
                'benefits': {'exp_bonus': 25, 'health_bonus': 15},
                'cultivation_flavor': '气如甘露，润泽周身'
            },
            
            # 炼气化神期
            {
                'level': 7, 'name': '炼气化神初期', 'title': '神识初醒',
                'description': '神识开始觉醒，专注力大幅提升',
                'required_exp': 2200,
                'benefits': {'exp_bonus': 30, 'health_bonus': 20, 'focus_bonus': 10},
                'cultivation_flavor': '气化神识，心灵澄澈'
            },
            {
                'level': 8, 'name': '炼气化神中期', 'title': '心神合一',
                'description': '心神合一，专注修炼效果倍增',
                'required_exp': 3000,
                'benefits': {'exp_bonus': 35, 'health_bonus': 25, 'focus_bonus': 20},
                'cultivation_flavor': '神清气爽，意念通达'
            },
            {
                'level': 9, 'name': '炼气化神后期', 'title': '神识通明',
                'description': '神识通明，理解力与专注力达到新高度',
                'required_exp': 4000,
                'benefits': {'exp_bonus': 40, 'health_bonus': 30, 'focus_bonus': 30},
                'cultivation_flavor': '神识如明镜，照彻本心'
            },
            
            # 炼神化虚期
            {
                'level': 10, 'name': '炼神化虚初期', 'title': '虚境初入',
                'description': '进入虚境，超凡脱俗',
                'required_exp': 5200,
                'benefits': {'exp_bonus': 50, 'health_bonus': 40, 'focus_bonus': 40},
                'cultivation_flavor': '神入虚境，心如止水'
            },
            {
                'level': 11, 'name': '炼神化虚中期', 'title': '虚实相融',
                'description': '虚实相融，达到天人合一的境界',
                'required_exp': 6500,
                'benefits': {'exp_bonus': 60, 'health_bonus': 50, 'focus_bonus': 50},
                'cultivation_flavor': '虚实不二，天人合一'
            },
            {
                'level': 12, 'name': '炼神化虚后期', 'title': '大虚若实',
                'description': '大虚若实，修炼境界已臻化境',
                'required_exp': 8000,
                'benefits': {'exp_bonus': 70, 'health_bonus': 60, 'focus_bonus': 60},
                'cultivation_flavor': '至虚极静，返璞归真'
            },
            
            # 至高境界
            {
                'level': 13, 'name': '炼虚合道初期', 'title': '合道初窥',
                'description': '开始领悟大道真谛',
                'required_exp': 10000,
                'benefits': {'exp_bonus': 80, 'health_bonus': 70, 'focus_bonus': 70},
                'cultivation_flavor': '道心初现，与天地同频'
            },
            {
                'level': 14, 'name': '炼虚合道中期', 'title': '道法自然',
                'description': '道法自然，举手投足皆含天地至理',
                'required_exp': 12000,
                'benefits': {'exp_bonus': 90, 'health_bonus': 80, 'focus_bonus': 80},
                'cultivation_flavor': '顺应天道，自然而然'
            },
            {
                'level': 15, 'name': '炼虚合道后期', 'title': '道心圆满',
                'description': '道心圆满，已近仙人之境',
                'required_exp': 15000,
                'benefits': {'exp_bonus': 100, 'health_bonus': 90, 'focus_bonus': 90},
                'cultivation_flavor': '道心如圆，无瑕无垢'
            },
            
            # 传说境界
            {
                'level': 16, 'name': '大乘初期', 'title': '大乘真修',
                'description': '进入大乘境界，超脱凡俗',
                'required_exp': 20000,
                'benefits': {'exp_bonus': 120, 'health_bonus': 100, 'focus_bonus': 100},
                'cultivation_flavor': '大乘之境，超凡入圣'
            },
            {
                'level': 17, 'name': '大乘中期', 'title': '圣者风范',
                'description': '圣者风范，德行天下',
                'required_exp': 25000,
                'benefits': {'exp_bonus': 140, 'health_bonus': 120, 'focus_bonus': 120},
                'cultivation_flavor': '圣人之德，泽被苍生'
            },
            {
                'level': 18, 'name': '大乘后期', 'title': '大乘巅峰',
                'description': '大乘巅峰，一步可达仙境',
                'required_exp': 30000,
                'benefits': {'exp_bonus': 160, 'health_bonus': 140, 'focus_bonus': 140},
                'cultivation_flavor': '巅峰在望，仙路可期'
            },
            
            # 仙人境界
            {
                'level': 19, 'name': '地仙', 'title': '逍遥地仙',
                'description': '成就地仙之位，逍遥自在',
                'required_exp': 40000,
                'benefits': {'exp_bonus': 200, 'health_bonus': 180, 'focus_bonus': 180},
                'cultivation_flavor': '地仙之境，逍遥天地间'
            },
            {
                'level': 20, 'name': '天仙', 'title': '无上天仙',
                'description': '至高无上的天仙境界，长生不老',
                'required_exp': 50000,
                'benefits': {'exp_bonus': 250, 'health_bonus': 200, 'focus_bonus': 200},
                'cultivation_flavor': '天仙果位，与天同寿'
            }
        ]
        
        for realm_data in realms:
            realm, created = CultivationRealm.objects.get_or_create(
                level=realm_data['level'],
                defaults=realm_data
            )
            if created:
                self.stdout.write(f"  创建境界: {realm.name}")
            else:
                self.stdout.write(f"  境界已存在: {realm.name}")

    def _init_cultivation_achievements(self):
        """初始化修炼成就"""
        self.stdout.write("初始化修炼成就...")
        
        achievements = [
            # 登录时长成就
            {
                'name': '初入修炼', 'description': '累计登录时长达到1小时',
                'achievement_type': 'LOGIN_TIME', 'condition_value': 60,
                'exp_reward': 50, 'rarity': 'common', 'display_order': 1,
                'cultivation_flavor': '踏出修炼第一步，路虽远，行则将至'
            },
            {
                'name': '勤勉修士', 'description': '累计登录时长达到10小时',
                'achievement_type': 'LOGIN_TIME', 'condition_value': 600,
                'exp_reward': 100, 'rarity': 'common', 'display_order': 2,
                'cultivation_flavor': '勤能补拙，持之以恒者必有所成'
            },
            {
                'name': '修炼达人', 'description': '累计登录时长达到100小时',
                'achievement_type': 'LOGIN_TIME', 'condition_value': 6000,
                'exp_reward': 500, 'rarity': 'rare', 'display_order': 3,
                'cultivation_flavor': '百炼成钢，千锤百炼铸真功'
            },
            {
                'name': '修炼宗师', 'description': '累计登录时长达到1000小时',
                'achievement_type': 'LOGIN_TIME', 'condition_value': 60000,
                'exp_reward': 2000, 'rarity': 'epic', 'display_order': 4,
                'cultivation_flavor': '宗师之路，万里征程只等闲'
            },
            
            # 专注时长成就
            {
                'name': '专注入门', 'description': '累计专注时长达到1小时',
                'achievement_type': 'FOCUS_TIME', 'condition_value': 60,
                'exp_reward': 100, 'rarity': 'common', 'display_order': 11,
                'cultivation_flavor': '心如止水，专注是修炼的根本'
            },
            {
                'name': '专注有成', 'description': '累计专注时长达到25小时',
                'achievement_type': 'FOCUS_TIME', 'condition_value': 1500,
                'exp_reward': 300, 'rarity': 'rare', 'display_order': 12,
                'cultivation_flavor': '专心致志，方能有所突破'
            },
            {
                'name': '专注大师', 'description': '累计专注时长达到100小时',
                'achievement_type': 'FOCUS_TIME', 'condition_value': 6000,
                'exp_reward': 1000, 'rarity': 'epic', 'display_order': 13,
                'cultivation_flavor': '心神合一，专注如剑锋所指'
            },
            {
                'name': '专注仙人', 'description': '累计专注时长达到500小时',
                'achievement_type': 'FOCUS_TIME', 'condition_value': 30000,
                'exp_reward': 3000, 'rarity': 'legendary', 'display_order': 14,
                'cultivation_flavor': '神识通明，万般皆在掌握中'
            },
            
            # 连续签到成就
            {
                'name': '三日不辍', 'description': '连续签到3天',
                'achievement_type': 'CHECKIN_STREAK', 'condition_value': 3,
                'exp_reward': 30, 'rarity': 'common', 'display_order': 21,
                'cultivation_flavor': '三日不断，修炼之心可嘉'
            },
            {
                'name': '一周不懈', 'description': '连续签到7天',
                'achievement_type': 'CHECKIN_STREAK', 'condition_value': 7,
                'exp_reward': 100, 'rarity': 'common', 'display_order': 22,
                'cultivation_flavor': '七日如一，恒心已现'
            },
            {
                'name': '一月精进', 'description': '连续签到30天',
                'achievement_type': 'CHECKIN_STREAK', 'condition_value': 30,
                'exp_reward': 500, 'rarity': 'rare', 'display_order': 23,
                'cultivation_flavor': '月圆月缺，修炼之心不变'
            },
            {
                'name': '百日筑基', 'description': '连续签到100天',
                'achievement_type': 'CHECKIN_STREAK', 'condition_value': 100,
                'exp_reward': 2000, 'rarity': 'epic', 'display_order': 24,
                'cultivation_flavor': '百日筑基，根基已固'
            },
            {
                'name': '一年如日', 'description': '连续签到365天',
                'achievement_type': 'CHECKIN_STREAK', 'condition_value': 365,
                'exp_reward': 5000, 'rarity': 'legendary', 'display_order': 25,
                'cultivation_flavor': '一年如一日，道心坚定如山'
            },
            
            # 境界突破成就
            {
                'name': '炼体成功', 'description': '突破至炼体三重',
                'achievement_type': 'REALM_UPGRADE', 'condition_value': 3,
                'exp_reward': 100, 'rarity': 'common', 'display_order': 31,
                'cultivation_flavor': '炼体有成，体魄强健'
            },
            {
                'name': '炼精化气', 'description': '突破至炼精化气期',
                'achievement_type': 'REALM_UPGRADE', 'condition_value': 4,
                'exp_reward': 300, 'rarity': 'rare', 'display_order': 32,
                'cultivation_flavor': '精化为气，修炼精进'
            },
            {
                'name': '炼气化神', 'description': '突破至炼气化神期',
                'achievement_type': 'REALM_UPGRADE', 'condition_value': 7,
                'exp_reward': 800, 'rarity': 'epic', 'display_order': 33,
                'cultivation_flavor': '气化神识，心灵澄澈'
            },
            {
                'name': '炼神化虚', 'description': '突破至炼神化虚期',
                'achievement_type': 'REALM_UPGRADE', 'condition_value': 10,
                'exp_reward': 1500, 'rarity': 'epic', 'display_order': 34,
                'cultivation_flavor': '神入虚境，超凡脱俗'
            },
            {
                'name': '合道大成', 'description': '突破至炼虚合道期',
                'achievement_type': 'REALM_UPGRADE', 'condition_value': 13,
                'exp_reward': 3000, 'rarity': 'legendary', 'display_order': 35,
                'cultivation_flavor': '合道大成，天人合一'
            },
            {
                'name': '大乘圣者', 'description': '突破至大乘期',
                'achievement_type': 'REALM_UPGRADE', 'condition_value': 16,
                'exp_reward': 5000, 'rarity': 'legendary', 'display_order': 36,
                'cultivation_flavor': '大乘圣者，德行天下'
            },
            {
                'name': '成仙得道', 'description': '突破至仙人境界',
                'achievement_type': 'REALM_UPGRADE', 'condition_value': 19,
                'exp_reward': 10000, 'rarity': 'mythical', 'display_order': 37,
                'cultivation_flavor': '成仙得道，长生不老'
            },
            
            # 专注会话成就
            {
                'name': '初尝甘露', 'description': '完成第一次有效专注会话',
                'achievement_type': 'FOCUS_SESSION', 'condition_value': 1,
                'exp_reward': 50, 'rarity': 'common', 'display_order': 41,
                'cultivation_flavor': '初尝专注甘露，心神渐静'
            },
            {
                'name': '专注小成', 'description': '完成10次有效专注会话',
                'achievement_type': 'FOCUS_SESSION', 'condition_value': 10,
                'exp_reward': 150, 'rarity': 'common', 'display_order': 42,
                'cultivation_flavor': '专注渐成，心境趋于平静'
            },
            {
                'name': '专注大成', 'description': '完成100次有效专注会话',
                'achievement_type': 'FOCUS_SESSION', 'condition_value': 100,
                'exp_reward': 800, 'rarity': 'rare', 'display_order': 43,
                'cultivation_flavor': '专注大成，心如明镜'
            },
            {
                'name': '专注圆满', 'description': '完成1000次有效专注会话',
                'achievement_type': 'FOCUS_SESSION', 'condition_value': 1000,
                'exp_reward': 3000, 'rarity': 'epic', 'display_order': 44,
                'cultivation_flavor': '专注圆满，神识通明'
            },
            
            # 总时长成就
            {
                'name': '修炼启蒙', 'description': '总修炼时长达到24小时',
                'achievement_type': 'TOTAL_TIME', 'condition_value': 1440,
                'exp_reward': 200, 'rarity': 'common', 'display_order': 51,
                'cultivation_flavor': '一日修炼，启蒙之路已开'
            },
            {
                'name': '修炼有恒', 'description': '总修炼时长达到168小时（一周）',
                'achievement_type': 'TOTAL_TIME', 'condition_value': 10080,
                'exp_reward': 600, 'rarity': 'rare', 'display_order': 52,
                'cultivation_flavor': '一周精进，恒心可嘉'
            },
            {
                'name': '修炼月圆', 'description': '总修炼时长达到720小时（一月）',
                'achievement_type': 'TOTAL_TIME', 'condition_value': 43200,
                'exp_reward': 2000, 'rarity': 'epic', 'display_order': 53,
                'cultivation_flavor': '月满则亏，但修炼之心如满月常圆'
            },
            {
                'name': '修炼年轮', 'description': '总修炼时长达到8760小时（一年）',
                'achievement_type': 'TOTAL_TIME', 'condition_value': 525600,
                'exp_reward': 8000, 'rarity': 'legendary', 'display_order': 54,
                'cultivation_flavor': '年复一年，修炼如年轮般坚韧'
            }
        ]
        
        for achievement_data in achievements:
            achievement, created = CultivationAchievement.objects.get_or_create(
                name=achievement_data['name'],
                defaults=achievement_data
            )
            if created:
                self.stdout.write(f"  创建成就: {achievement.name}")
            else:
                self.stdout.write(f"  成就已存在: {achievement.name}") 